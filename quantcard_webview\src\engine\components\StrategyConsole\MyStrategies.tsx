/**
 * 📊 我的策略页面组件
 * 展示和管理用户的策略组，支持执行、编辑、删除等操作
 */

import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useStrategyState, useUIState, useAuth } from '../../../store/hooks'
import { listStrategyGroups } from '../../../services/api/strategy'
import StrategyCard from './StrategyCard'
import StrategyViewModal from './StrategyViewModal'
import StrategySettingsModal from './StrategySettingsModal'
import SignalResultModal from './SignalResultModal'

// 🎨 样式组件
const PageSection = styled.div`
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
`

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
`

const LoadingState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
`

// 顶部操作栏
const TopActionBar = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
`

// 正方形创建按钮
const CreateButton = styled(motion.button)`
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  flex-shrink: 0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`

// 策略统计展示组件 - 紧凑型设计
const StatsDisplay = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
`

const StatBadge = styled.div<{ $color: string; $bgColor: string }>`
  background: ${props => props.$bgColor};
  border: 1px solid ${props => props.$color};
  color: ${props => props.$color};
  border-radius: 16px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;

  .icon {
    font-size: 0.8rem;
  }

  .value {
    font-weight: 700;
  }
`

const RefreshButton = styled(motion.button)`
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  flex-shrink: 0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`

const StrategiesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 关键：允许flex子项收缩 */

  /* 美观的滚动条样式 - 参考弹出面板 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;

    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }
  }
`

// 🎮 我的策略页面组件
function MyStrategiesPage() {
  const {
    groups,
    loading,
    error,
    loadGroups,
    executeGroup,
    startGroup,
    stopGroup,
    deleteGroup
  } = useStrategyState()
  const { switchScene } = useUIState()
  const { isAuthenticated, loading: authLoading, token } = useAuth()

  // 🎨 模态框状态
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [settingsModalOpen, setSettingsModalOpen] = useState(false)
  const [signalResultModalOpen, setSignalResultModalOpen] = useState(false)
  const [selectedStrategyId, setSelectedStrategyId] = useState<string | null>(null)
  const [executingId, setExecutingId] = useState<string | null>(null)
  const [executionResult, setExecutionResult] = useState<{
    signals: any[]
    strategyName: string
  } | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  // 🔄 本地加载策略组函数，使用当前token
  const loadGroupsWithToken = async () => {
    if (!token) {
      console.log('❌ 没有token，无法加载策略组')
      return
    }

    try {
      console.log('🔄 使用当前token加载策略组:', token.substring(0, 50) + '...')
      await listStrategyGroups(1, 50, token)
      // 成功后调用原始的loadGroups来更新状态
      loadGroups()
    } catch (error) {
      console.error('加载策略组失败:', error)
    }
  }

  // 🔄 加载策略组 - 使用当前token确保同步
  useEffect(() => {
    // 只有在认证状态明确且有token，且没有数据且没有错误时才加载
    if (isAuthenticated && !authLoading && token && groups.length === 0 && !loading && !error) {
      console.log('MyStrategiesPage mounted, loading groups with token:', token.substring(0, 50) + '...')
      loadGroupsWithToken()
    }
    // 移除loadGroups依赖，避免无限循环
  }, [isAuthenticated, authLoading, token])

  // 监听认证状态变化 - 防止token过期后的无限重试
  useEffect(() => {
    // 如果用户未认证，停止所有加载操作
    if (!isAuthenticated && !authLoading) {
      console.log('🔄 用户未认证，停止策略组加载')
      // 不需要清理数据，让用户看到之前的数据
    }
  }, [isAuthenticated, authLoading])

  // 监听认证更新事件
  useEffect(() => {
    const handleAuthUpdated = () => {
      console.log('🔄 收到认证更新事件，等待状态同步后重新加载策略组')
      // 等待一小段时间确保状态完全同步
      setTimeout(() => {
        if (isAuthenticated && !authLoading && token) {
          console.log('🔄 状态已同步，开始加载策略组，使用token:', token.substring(0, 50) + '...')
          loadGroupsWithToken()
        }
      }, 200) // 200ms延迟确保状态同步
    }

    window.addEventListener('auth-updated', handleAuthUpdated)

    return () => {
      window.removeEventListener('auth-updated', handleAuthUpdated)
    }
  }, [isAuthenticated, authLoading, token]) // 添加token依赖

  // 调试日志
  useEffect(() => {
    console.log('MyStrategiesPage - Strategy data:', { groups, loading, error })
    console.log('MyStrategiesPage - Auth state:', { isAuthenticated, authLoading })
  }, [groups, loading, error, isAuthenticated, authLoading])

  // 🎯 处理策略执行
  const handleExecute = async (strategyId: string) => {
    setExecutingId(strategyId)
    try {
      const result = await executeGroup(strategyId)
      console.log('策略执行成功:', strategyId, result)

      // 如果有信号结果，显示结果模态框
      if (result?.data?.signals && result.data.signals.length > 0) {
        const strategy = groups.find(g => g.id === strategyId)
        setExecutionResult({
          signals: result.data.signals,
          strategyName: strategy?.name || '策略'
        })
        setSignalResultModalOpen(true)
      }
    } catch (error) {
      console.error('策略执行失败:', error)
    } finally {
      setExecutingId(null)
    }
  }

  // 🚀 处理策略启动
  const handleStart = async (strategyId: string) => {
    setExecutingId(strategyId)
    try {
      await startGroup(strategyId)
      console.log('策略启动成功:', strategyId)
    } catch (error) {
      console.error('策略启动失败:', error)
    } finally {
      setExecutingId(null)
    }
  }

  // 🛑 处理策略停止
  const handleStop = async (strategyId: string) => {
    setExecutingId(strategyId)
    try {
      await stopGroup(strategyId)
      console.log('策略停止成功:', strategyId)
    } catch (error) {
      console.error('策略停止失败:', error)
    } finally {
      setExecutingId(null)
    }
  }

  // 📊 处理查看策略
  const handleView = (strategyId: string) => {
    setSelectedStrategyId(strategyId)
    setViewModalOpen(true)
  }

  // ⚙️ 处理策略设置
  const handleSettings = (strategyId: string) => {
    setSelectedStrategyId(strategyId)
    setSettingsModalOpen(true)
  }

  // 🗑️ 处理删除策略
  const handleDelete = async (strategyId: string) => {
    try {
      await deleteGroup(strategyId)
      console.log('策略删除成功:', strategyId)
    } catch (error) {
      console.error('策略删除失败:', error)
    }
  }

  // 🔄 处理刷新
  const handleRefresh = async () => {
    // 检查认证状态和token
    if (!isAuthenticated || !token) {
      console.log('🔄 用户未认证或没有token，无法刷新数据')
      return
    }

    setRefreshing(true)
    try {
      await loadGroupsWithToken()
    } catch (error) {
      console.error('刷新失败:', error)
      // 如果是token过期，不需要特殊处理，因为API层已经处理了
    } finally {
      setRefreshing(false)
    }
  }

  // 计算策略统计
  const stats = {
    total: groups.length,
    active: groups.filter(g => g.status === 'active').length,
    inactive: groups.filter(g => g.status === 'inactive').length,
    error: groups.filter(g => g.status === 'error').length
  }

  return (
    <PageSection>
      {/* 顶部操作栏 */}
      <TopActionBar>
        {/* 正方形创建按钮 */}
        <CreateButton
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => switchScene('StrategyCreation')}
          title="创建新策略"
        >
          ➕
        </CreateButton>

        {/* 策略统计展示 - 紧凑型徽章设计 */}
        <StatsDisplay>
          <StatBadge $color="#6366f1" $bgColor="#6366f120">
            <span className="icon">📊</span>
            <span className="value">{stats.total}</span>
          </StatBadge>

          {stats.active > 0 && (
            <StatBadge $color="#10b981" $bgColor="#10b98120">
              <span className="icon">▶️</span>
              <span className="value">{stats.active}</span>
            </StatBadge>
          )}

          {stats.inactive > 0 && (
            <StatBadge $color="#6b7280" $bgColor="#6b728020">
              <span className="icon">⏸️</span>
              <span className="value">{stats.inactive}</span>
            </StatBadge>
          )}

          {stats.error > 0 && (
            <StatBadge $color="#ef4444" $bgColor="#ef444420">
              <span className="icon">⚠️</span>
              <span className="value">{stats.error}</span>
            </StatBadge>
          )}
        </StatsDisplay>

        {/* 刷新按钮 */}
        <RefreshButton
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleRefresh}
          disabled={refreshing}
          animate={refreshing ? { rotate: 360 } : { rotate: 0 }}
          transition={refreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
          title="刷新策略列表"
        >
          🔄
        </RefreshButton>
      </TopActionBar>
      
      {loading ? (
        <LoadingState>加载中...</LoadingState>
      ) : error ? (
        <EmptyState>
          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⚠️</div>
          <div>加载失败: {error}</div>
          {!isAuthenticated && (
            <div style={{ marginTop: '1rem', fontSize: '0.9rem', color: '#666' }}>
              请重新登录后再试
            </div>
          )}
        </EmptyState>
      ) : groups.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
          <div>暂无策略组合</div>
        </EmptyState>
      ) : (
        <StrategiesList>
          {groups.map((strategy) => (
            <StrategyCard
              key={strategy.id}
              strategy={strategy}
              onExecute={handleExecute}
              onStart={handleStart}
              onStop={handleStop}
              onView={handleView}
              onSettings={handleSettings}
              onDelete={handleDelete}
              loading={loading}
              executingId={executingId}
            />
          ))}
        </StrategiesList>
      )}

      {/* 查看模态框 */}
      <StrategyViewModal
        isOpen={viewModalOpen}
        onClose={() => {
          setViewModalOpen(false)
          setSelectedStrategyId(null)
        }}
        strategyId={selectedStrategyId}
      />

      {/* 设置模态框 */}
      <StrategySettingsModal
        isOpen={settingsModalOpen}
        onClose={() => {
          setSettingsModalOpen(false)
          setSelectedStrategyId(null)
        }}
        strategyId={selectedStrategyId}
      />

      {/* 信号结果模态框 */}
      <SignalResultModal
        isOpen={signalResultModalOpen}
        onClose={() => {
          setSignalResultModalOpen(false)
          setExecutionResult(null)
        }}
        signals={executionResult?.signals || []}
        strategyName={executionResult?.strategyName}
      />
    </PageSection>
  )
}

export default MyStrategiesPage
