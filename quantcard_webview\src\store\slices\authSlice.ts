/**
 * 🔐 认证状态切片 - 管理用户认证、访客模式和会话状态
 * 支持用户登录、访客模式、Token管理和状态持久化
 */

import type { SliceCreator } from '../types/store'
import { AuthTokenManager } from '../../utils/authUtils'

// 🔐 用户信息接口
export interface UserInfo {
  id: string
  username: string
  email?: string
  is_guest: boolean
  is_active: boolean
  is_superuser?: boolean
  permissions?: string[]
  avatar?: string
  created_at?: string
  expires_at?: string
}

// 🔐 认证状态接口
export interface AuthSlice {
  // 状态
  isAuthenticated: boolean
  isGuest: boolean
  user: UserInfo | null
  token: string | null
  loading: boolean
  error?: string

  // 方法
  login: (username: string, password: string, remember?: boolean) => Promise<boolean>
  guestLogin: () => Promise<boolean>
  logout: () => void
  refreshToken: () => Promise<boolean>
  updateUserInfo: (userInfo: Partial<UserInfo>) => void
  checkAuthStatus: () => void
  clearError: () => void
}

// 🔐 创建认证切片
export const createAuthSlice: SliceCreator<AuthSlice> = (set, get) => ({
  // 🔐 初始状态
  isAuthenticated: false,
  isGuest: false,
  user: null,
  token: null,
  loading: false,
  error: undefined,

  // 🔑 用户登录
  login: async (username: string, password: string, remember: boolean = false) => {
    const currentState = get()
    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 调用登录API
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username,
          password,
          remember: remember.toString()
        })
      })

      // 检查HTTP状态
      if (!response.ok) {
        // 根据状态码返回友好的错误消息
        let errorMessage = '登录失败'
        
        switch (response.status) {
          case 401:
            errorMessage = '用户名或密码错误'
            break
          case 403:
            errorMessage = '账户已被禁用'
            break
          case 429:
            errorMessage = '登录尝试过于频繁，请稍后再试'
            break
          case 500:
            errorMessage = '服务器异常，请稍后重试'
            break
          default:
            errorMessage = '网络连接异常，请检查网络设置'
        }
        
        set({
          ...get(),
          loading: false,
          error: errorMessage
        })
        return false
      }

      const result = await response.json()

      if (result.success && result.data) {
        const { access_token, user } = result.data
        
        // 保存认证信息
        const authData = {
          token: access_token,
          user: {
            ...user,
            is_guest: false
          },
          isAuthenticated: true,
          isGuest: false,
          loading: false
        }

        // 使用统一认证工具保存数据
        console.log('💾 保存认证数据:', { token: access_token.substring(0, 50) + '...', user: authData.user.username })
        AuthTokenManager.saveAuthData(access_token, authData.user, remember, false)

        // 更新Zustand状态
        console.log('🔄 更新Zustand认证状态')
        set({
          ...get(),
          ...authData
        })

        // 验证状态更新
        const currentState = get()
        console.log('✅ 认证状态已更新:', {
          isAuthenticated: currentState.isAuthenticated,
          token: currentState.token?.substring(0, 50) + '...',
          user: currentState.user?.username
        })

        return true
      } else {
        set({
          ...get(),
          loading: false,
          error: result.message || '登录失败，请检查用户名和密码'
        })
        return false
      }

    } catch (error) {
      console.error('登录过程中发生错误:', error)
      set({
        ...get(),
        loading: false,
        error: '网络连接异常，请检查网络设置后重试'
      })
      return false
    }
  },

  // 👤 访客登录
  guestLogin: async () => {
    const currentState = get()
    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 调用访客登录API
      const response = await fetch('/api/v1/auth/guest-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()

      if (result.success && result.data) {
        const { access_token, user, guest_id, permissions, expires_at } = result.data
        
        // 保存访客认证信息
        const guestAuthData = {
          token: access_token,
          user: {
            id: guest_id,
            username: user.username,
            is_guest: true,
            is_active: true,
            permissions,
            expires_at
          },
          isAuthenticated: true,
          isGuest: true,
          loading: false
        }

        // 使用统一认证工具保存访客数据
        AuthTokenManager.saveAuthData(access_token, guestAuthData.user, false, true)

        set({
          ...get(),
          ...guestAuthData
        })

        return true
      } else {
        throw new Error(result.message || '访客登录失败')
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '访客登录失败'
      set({
        ...get(),
        loading: false,
        error: errorMessage
      })
      return false
    }
  },

  // 🚪 登出
  logout: () => {
    console.log('👋 用户登出')

    // 使用统一认证工具清理所有存储
    AuthTokenManager.clearAllAuthData()

    // 重置认证状态
    set({
      ...get(),
      isAuthenticated: false,
      isGuest: false,
      user: null,
      token: null,
      loading: false,
      error: undefined
    })

    // 可以添加登出成功的通知
    console.log('✅ 登出成功，状态已重置')
  },

  // 🔄 刷新Token
  refreshToken: async () => {
    const currentState = get()
    
    if (!currentState.token) {
      return false
    }

    try {
      // 这里应该调用刷新Token的API
      // 暂时返回true表示Token仍然有效
      return true
    } catch (error) {
      // Token刷新失败，执行登出
      get().logout()
      return false
    }
  },

  // 👤 更新用户信息
  updateUserInfo: (userInfo: Partial<UserInfo>) => {
    const currentState = get()
    if (currentState.user) {
      const updatedUser = { ...currentState.user, ...userInfo }
      
      set({
        ...currentState,
        user: updatedUser
      })

      // 更新存储
      const storageKey = currentState.isGuest ? 'quantcard_guest_auth' : 'quantcard_auth'
      const storage = currentState.isGuest ? sessionStorage : 
        (localStorage.getItem('quantcard_auth') ? localStorage : sessionStorage)
      
      const authData = JSON.parse(storage.getItem(storageKey) || '{}')
      authData.user = updatedUser
      storage.setItem(storageKey, JSON.stringify(authData))
    }
  },

  // ✅ 检查认证状态 - 使用统一认证工具
  checkAuthStatus: () => {
    try {
      console.log('🔍 正在检查认证状态...')

      // 获取当前有效的用户信息和Token（不清理过期数据，避免竞态条件）
      const currentUser = AuthTokenManager.getCurrentUser()
      const currentToken = AuthTokenManager.getToken()

      if (currentUser && currentToken) {
        console.log('✅ 恢复认证状态')
        set({
          ...get(),
          isAuthenticated: true,
          isGuest: currentUser.is_guest || false,
          user: currentUser,
          token: currentToken,
          loading: false,
          error: undefined
        })
        return
      }

      // 没有有效的认证信息，设置为未认证状态
      console.log('❌ 未找到有效认证信息，设置为未认证状态')
      set({
        ...get(),
        isAuthenticated: false,
        isGuest: false,
        user: null,
        token: null,
        loading: false,
        error: undefined
      })

    } catch (error) {
      console.error('🚨 检查认证状态失败:', error)
      // 发生错误时，设置为未认证状态
      set({
        ...get(),
        isAuthenticated: false,
        isGuest: false,
        user: null,
        token: null,
        loading: false,
        error: undefined
      })
    }
  },

  // 🔄 处理token过期
  handleTokenExpired: () => {
    console.log('🔄 Token已过期，清理认证状态')
    AuthTokenManager.clearAllAuthData()
    set({
      ...get(),
      isAuthenticated: false,
      isGuest: false,
      user: null,
      token: null,
      loading: false,
      error: 'Token已过期，请重新登录'
    })
  },

  // 🧹 清理错误
  clearError: () => {
    const currentState = get()
    set({
      ...currentState,
      error: undefined
    })
  }
})

 