/**
 * 🔐 认证Hook - 管理用户认证状态和操作
 */

import { useStore } from '../index'
import type { UserInfo } from '../slices/authSlice'

export const useAuth = () => {
  // 🔐 认证状�?
  const isAuthenticated = useStore(state => state.isAuthenticated)
  const isGuest = useStore(state => state.isGuest)
  const user = useStore(state => state.user)
  const token = useStore(state => state.token)
  const loading = useStore(state => state.loading)
  const error = useStore(state => state.error)

  // 🔐 认证操作
  const login = useStore(state => state.login)
  const guestLogin = useStore(state => state.guestLogin)
  const logout = useStore(state => state.logout)
  const refreshToken = useStore(state => state.refreshToken)
  const updateUserInfo = useStore(state => state.updateUserInfo)
  const checkAuthStatus = useStore(state => state.checkAuthStatus)
  const handleTokenExpired = useStore(state => state.handleTokenExpired)
  const clearError = useStore(state => state.clearError)

  // 🔐 便捷方法
  const isLoggedIn = isAuthenticated && !isGuest
  const hasPermission = (permission: string) => {
    if (!user) return false
    if (user.is_superuser) return true
    if (user.permissions) {
      return user.permissions.includes(permission)
    }
    // 默认权限检�?
    if (isGuest) {
      return ['browse', 'view_worldmap', 'view_strategies'].includes(permission)
    }
    return true // 登录用户默认有权�?
  }

  const requireLogin = (action: string = "进行此操作") => {
    if (!isAuthenticated) {
      throw new Error(`请先登录后再${action}`)
    }
    if (isGuest) {
      throw new Error(`访客权限不足，请登录后再${action}`)
    }
  }

  return {
    // 状�?
    isAuthenticated,
    isGuest,
    isLoggedIn,
    user,
    token,
    loading,
    error,

    // 操作
    login,
    guestLogin,
    logout,
    refreshToken,
    updateUserInfo,
    checkAuthStatus,
    handleTokenExpired,
    clearError,

    // 工具方法
    hasPermission,
    requireLogin
  }
} 
