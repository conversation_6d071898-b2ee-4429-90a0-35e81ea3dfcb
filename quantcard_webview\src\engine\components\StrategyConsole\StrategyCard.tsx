/**
 * 📊 增强版策略卡片组件
 * 移动端游戏化设计，支持执行、查看、设置等完整功能
 */

import React, { useState } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import type { StrategyGroup } from '../../../types/game'
import StrategyExecutionPanel from './StrategyExecutionPanel'

// 🎨 样式组件
const CardContainer = styled(motion.div)`
  background: #ffffff;
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }
`

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`

const StrategyInfo = styled.div`
  flex: 1;
`

const StrategyName = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
`

const StrategyMeta = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
`

const Badge = styled.span<{ $variant: 'status' | 'type' | 'count' | 'mode' }>`
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  
  ${props => {
    switch (props.$variant) {
      case 'status':
        return `
          background: ${props.children?.toString().includes('运行') ? '#10b98120' : '#6b728020'};
          color: ${props.children?.toString().includes('运行') ? '#10b981' : '#6b7280'};
        `
      case 'type':
        return `
          background: #3b82f620;
          color: #3b82f6;
        `
      case 'count':
        return `
          background: #f59e0b20;
          color: #f59e0b;
        `
      case 'mode':
        return `
          background: #8b5cf620;
          color: #8b5cf6;
        `
      default:
        return ''
    }
  }}
`

const StrategyDescription = styled.p`
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 1rem 0;
  line-height: 1.5;
`



const ActionButtons = styled.div`
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;

  @media (max-width: 768px) {
    gap: 0.5rem;
    flex-wrap: wrap;
  }
`

// 执行面板样式 - 简化版本
const ExecutionPanelContainer = styled.div`
  margin-top: 16px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
  position: relative;
  z-index: 2;
  padding: 12px;
`

const ActionButton = styled(motion.button)<{ $variant: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  @media (max-width: 768px) {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
    min-height: 44px; /* 确保触摸目标足够大 */
  }
  
  ${props => {
    switch (props.$variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        `
      case 'secondary':
        return `
          background: #f8fafc;
          color: #475569;
          border: 1px solid #e2e8f0;
        `
      case 'danger':
        return `
          background: #fee2e2;
          color: #dc2626;
          border: 1px solid #fecaca;
        `
      default:
        return ''
    }
  }}
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`

const StatusIndicator = styled(motion.div)<{ $active: boolean }>`
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.$active ? '#10b981' : '#6b7280'};
  box-shadow: 0 0 8px ${props => props.$active ? '#10b98150' : '#6b728050'};
`

// 🎮 策略卡片组件属性
interface StrategyCardProps {
  strategy: StrategyGroup
  onExecute: (strategyId: string) => void
  onStart: (strategyId: string) => void
  onStop: (strategyId: string) => void
  onView: (strategyId: string) => void
  onSettings: (strategyId: string) => void
  onDelete: (strategyId: string) => void
  loading?: boolean
  executingId?: string | null
}

// 🎮 策略卡片组件
function StrategyCard({
  strategy,
  onExecute,
  onStart,
  onStop,
  onView,
  onSettings,
  onDelete,
  loading = false,
  executingId = null
}: StrategyCardProps) {
  const [showActions, setShowActions] = useState(false)
  const [showExecutionPanel, setShowExecutionPanel] = useState(false)

  const isActive = strategy.status === 'active'
  const isExecuting = executingId === strategy.id
  const isTiming = strategy.group_type === 'timing'

  // 调试日志 - 跟踪执行面板状态
  React.useEffect(() => {
    console.log(`策略 ${strategy.id} 执行面板状态:`, {
      showExecutionPanel,
      isExecuting,
      executingId,
      strategyId: strategy.id
    })
  }, [showExecutionPanel, isExecuting, executingId, strategy.id])

  // 🎯 处理主要操作 - 按照旧版本的逻辑
  const handleMainAction = () => {
    // 显示执行面板 - 无论之前的状态如何
    setShowExecutionPanel(true)

    // 短暂延迟后调用执行函数，确保面板已经显示
    setTimeout(() => {
      if (isTiming) {
        // 择时策略：切换启动/停止
        if (isActive) {
          onStop(strategy.id)
        } else {
          onStart(strategy.id)
        }
      } else {
        // 选股策略：执行
        onExecute(strategy.id)
      }
    }, 100)
  }

  // 🎨 状态指示器动画
  const statusVariants = {
    active: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut" as const
      }
    },
    inactive: {
      scale: 1
    }
  }

  return (
    <CardContainer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setShowActions(true)}
      onHoverEnd={() => setShowActions(false)}
    >
      {/* 状态指示器 */}
      <StatusIndicator
        $active={isActive}
        variants={statusVariants}
        animate={isActive ? 'active' : 'inactive'}
      />
      
      <CardHeader>
        <StrategyInfo>
          <StrategyName>{strategy.name}</StrategyName>
          
          <StrategyMeta>
            <Badge $variant="status">
              {isActive ? '🟢 运行中' : '⚪ 已停止'}
            </Badge>
            <Badge $variant="type">
              {isTiming ? '⏰ 择时策略' : '🎯 选股策略'}
            </Badge>
            <Badge $variant="count">
              📊 {strategy.cards?.length || 0} 张卡片
            </Badge>
            {strategy.execution_mode && (
              <Badge $variant="mode">
                {strategy.execution_mode === 'sequential' ? '🔄 串行' : '⚡ 并行'}
              </Badge>
            )}
          </StrategyMeta>
        </StrategyInfo>
      </CardHeader>
      
      {strategy.description && (
        <StrategyDescription>
          {strategy.description}
        </StrategyDescription>
      )}
      

      
      {/* 操作按钮 */}
      <ActionButtons>
        <ActionButton
          $variant="secondary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onView(strategy.id)}
        >
          📊 查看
        </ActionButton>
        
        <ActionButton
          $variant="secondary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onSettings(strategy.id)}
        >
          ⚙️ 设置
        </ActionButton>

        <ActionButton
          $variant="primary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleMainAction}
          disabled={isExecuting}
        >
          {isExecuting ? (
            <>⏳ 执行中...</>
          ) : isTiming ? (
            isActive ? '⏹️ 停止' : '▶️ 启动'
          ) : (
            '🚀 执行'
          )}
        </ActionButton>
      </ActionButtons>

      {/* 执行面板 - 当showExecutionPanel为true时显示 */}
      {showExecutionPanel && (
        <ExecutionPanelContainer>
          <StrategyExecutionPanel
            strategyId={strategy.id}
            onClose={() => {
              console.log(`用户手动关闭执行面板，策略ID: ${strategy.id}`)
              setShowExecutionPanel(false)
            }}
          />
        </ExecutionPanelContainer>
      )}
    </CardContainer>
  )
}

// 使用React.memo优化，避免不必要的重新渲染
export default React.memo(StrategyCard)
