"""
统一认证系统
整合所有认证功能：JWT、密码、用户认证、访客模式、WebSocket认证和权限管理
"""
import json
import uuid
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from jose import jwt, JWTError
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import OAuth2PasswordBearer
from pydantic import BaseModel, ValidationError

from .config import settings
from ..models.user import User

logger = logging.getLogger(__name__)

# OAuth2 scheme for token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")

# 统一密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class TokenPayload(BaseModel):
    """Token载荷模型"""
    sub: str  # 用户ID
    exp: float  # 过期时间
    iat: float  # 签发时间
    type: str = "access"  # Token类型
    is_guest: bool = False  # 是否为访客

class GuestSession(BaseModel):
    """访客会话模型"""
    guest_id: str
    created_at: datetime
    expires_at: datetime
    permissions: List[str] = ["browse", "view_worldmap"]
    session_data: Dict[str, Any] = {}

class UnifiedAuthManager:
    """统一认证管理器 - 整合所有认证功能"""

    def __init__(self):
        self.guest_sessions: Dict[str, GuestSession] = {}

    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None, is_guest: bool = False) -> str:
        """统一Token创建方法"""
        try:
            to_encode = data.copy()

            if expires_delta:
                expire = datetime.utcnow() + expires_delta
            else:
                # 访客Token有效期较短
                expire_minutes = 60 if is_guest else settings.ACCESS_TOKEN_EXPIRE_MINUTES
                expire = datetime.utcnow() + timedelta(minutes=expire_minutes)

            to_encode.update({
                "exp": expire.timestamp(),
                "iat": datetime.utcnow().timestamp(),
                "type": "access",
                "is_guest": is_guest
            })

            encoded_jwt = jwt.encode(
                to_encode,
                settings.SECRET_KEY,
                algorithm=settings.ALGORITHM
            )
            logger.info(f"成功创建访问令牌，用户: {data.get('sub')}, 访客模式: {is_guest}")
            return encoded_jwt
        except Exception as e:
            logger.error(f"创建访问令牌失败: {str(e)}", exc_info=True)
            raise

    def hash_password(self, password: str) -> str:
        """统一密码哈希方法"""
        return pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """统一密码验证方法"""
        return pwd_context.verify(plain_password, hashed_password)

    def verify_token(self, token: str) -> TokenPayload:
        """验证令牌"""
        try:
            logger.info(f"🔍 验证Token: {token[:50]}...")
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
            )
            logger.info(f"✅ Token解码成功: {payload}")

            token_data = TokenPayload(
                sub=payload.get("sub"),
                exp=payload.get("exp"),
                iat=payload.get("iat"),
                type=payload.get("type", "access"),
                is_guest=payload.get("is_guest", False)
            )

            # 检查是否过期
            current_time = datetime.utcnow().timestamp()
            logger.info(f"⏰ Token过期时间: {token_data.exp}, 当前时间: {current_time}")
            if token_data.exp < current_time:
                logger.warning(f"❌ Token已过期: exp={token_data.exp}, now={current_time}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token已过期",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            logger.info(f"✅ Token验证成功: {token_data}")
            return token_data

        except JWTError as e:
            logger.error(f"❌ Token验证失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """统一用户认证方法"""
        try:
            user = await User.get_by_username(username)
            if not user:
                return None

            if not self.verify_password(password, user.hashed_password):
                return None

            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户已被禁用"
                )

            return user
        except Exception as e:
            logger.error(f"用户认证失败: {str(e)}")
            return None

    def create_guest_session(self, guest_id: Optional[str] = None) -> GuestSession:
        """创建访客会话"""
        if not guest_id:
            guest_id = f"guest_{uuid.uuid4().hex[:8]}"
            
        session = GuestSession(
            guest_id=guest_id,
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=2),  # 访客会话2小时过期
            permissions=["browse", "view_worldmap", "view_strategies"],
            session_data={}
        )
        
        self.guest_sessions[guest_id] = session
        return session

    def get_guest_session(self, guest_id: str) -> Optional[GuestSession]:
        """获取访客会话"""
        session = self.guest_sessions.get(guest_id)
        if session and session.expires_at > datetime.utcnow():
            return session
        elif session:
            # 清理过期会话
            del self.guest_sessions[guest_id]
        return None

    def update_guest_session(self, guest_id: str, session_data: Dict[str, Any]) -> bool:
        """更新访客会话数据"""
        session = self.get_guest_session(guest_id)
        if session:
            session.session_data.update(session_data)
            return True
        return False

    def cleanup_expired_guest_sessions(self):
        """清理过期访客会话"""
        current_time = datetime.utcnow()
        expired_sessions = [
            guest_id for guest_id, session in self.guest_sessions.items()
            if session.expires_at <= current_time
        ]
        
        for guest_id in expired_sessions:
            del self.guest_sessions[guest_id]

    async def get_user_from_token(self, token: str) -> Dict[str, Any]:
        """从Token获取用户信息"""
        token_payload = self.verify_token(token)
        
        if token_payload.is_guest:
            # 访客用户
            session = self.get_guest_session(token_payload.sub)
            if not session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="访客会话已过期"
                )
                
            return {
                "id": session.guest_id,
                "username": f"访客_{session.guest_id[-8:]}",
                "is_guest": True,
                "is_active": True,
                "is_superuser": False,
                "permissions": session.permissions,
                "session_data": session.session_data,
                "created_at": session.created_at,
                "expires_at": session.expires_at
            }
        else:
            # 正常用户
            user = await User.get_by_id(token_payload.sub)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
                
            return {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_guest": False,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "last_login": user.last_login,
                "avatar": user.avatar,
                "favorites": user.favorites
            }

    def check_permission(self, user: Dict[str, Any], required_permission: str) -> bool:
        """检查用户权限"""
        if user.get("is_guest"):
            # 访客权限检查
            permissions = user.get("permissions", [])
            return required_permission in permissions
        else:
            # 正常用户权限检查
            if user.get("is_superuser"):
                return True
                
            # 正常用户默认权限
            default_permissions = [
                "browse", "view_worldmap", "view_strategies", 
                "use_strategies", "create_strategies", "manage_inventory"
            ]
            return required_permission in default_permissions

    def require_permission(self, required_permission: str):
        """权限装饰器工厂"""
        def permission_decorator(func):
            async def wrapper(*args, **kwargs):
                # 获取当前用户（需要在参数中查找）
                current_user = None
                for arg in args:
                    if isinstance(arg, dict) and "id" in arg:
                        current_user = arg
                        break
                
                if not current_user:
                    for value in kwargs.values():
                        if isinstance(value, dict) and "id" in value:
                            current_user = value
                            break
                
                if not current_user:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="需要认证"
                    )
                
                if not self.check_permission(current_user, required_permission):
                    if current_user.get("is_guest"):
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="访客权限不足，请登录后再试"
                        )
                    else:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="权限不足"
                        )
                
                return await func(*args, **kwargs)
            return wrapper
        return permission_decorator

    async def get_current_user_from_token(self, token: str) -> Dict[str, Any]:
        """统一从Token获取当前用户方法"""
        # 验证token并获取payload
        token_payload = self.verify_token(token)

        if token_payload.is_guest:
            # 访客用户
            session = self.get_guest_session(token_payload.sub)
            if not session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="访客会话已过期"
                )

            return {
                "id": session.guest_id,
                "username": f"访客_{session.guest_id[-8:]}",
                "is_guest": True,
                "is_active": True,
                "is_superuser": False,
                "permissions": session.permissions,
                "session_data": session.session_data,
                "created_at": session.created_at,
                "expires_at": session.expires_at
            }
        else:
            # 正常用户
            user = await User.get_by_id(token_payload.sub)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )

            return {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_guest": False,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "last_login": user.last_login,
                "avatar": user.avatar,
                "favorites": user.favorites
            }

# 全局统一认证管理器实例
auth_manager = UnifiedAuthManager()

# 统一对外接口函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None, is_guest: bool = False) -> str:
    """创建访问令牌"""
    return auth_manager.create_access_token(data, expires_delta, is_guest)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return auth_manager.verify_password(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return auth_manager.hash_password(password)

async def authenticate_user(username: str, password: str) -> Optional[User]:
    """用户认证"""
    return await auth_manager.authenticate_user(username, password)

async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """获取当前用户（FastAPI依赖）"""
    logger.info(f"🔐 获取当前用户，Token: {token[:50] if token else 'None'}...")
    try:
        user = await auth_manager.get_current_user_from_token(token)
        logger.info(f"✅ 用户认证成功: {user.get('username', 'Unknown')}")
        return user
    except Exception as e:
        logger.error(f"❌ 用户认证失败: {str(e)}")
        raise

async def get_websocket_user(websocket) -> Optional[str]:
    """WebSocket用户认证"""
    try:
        from fastapi import WebSocket
        # 从查询参数获取token
        token = websocket.query_params.get("token")
        if not token:
            logger.error("WebSocket连接缺少token")
            await websocket.close(code=1008)
            return None

        # 解析token
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)

        # 检查token是否过期
        if token_data.exp < datetime.utcnow().timestamp():
            logger.error("WebSocket连接使用的token已过期")
            await websocket.close(code=1008)
            return None

        # 获取用户
        user = await User.get_by_id(token_data.sub)
        if not user:
            logger.error(f"WebSocket连接用户不存在，用户ID: {token_data.sub}")
            await websocket.close(code=1008)
            return None

        return str(user.id)
    except (JWTError, ValidationError) as e:
        logger.error(f"WebSocket连接Token验证失败: {str(e)}")
        await websocket.close(code=1008)
        return None
    except Exception as e:
        logger.error(f"WebSocket连接认证时出现未知错误: {str(e)}", exc_info=True)
        await websocket.close(code=1011)
        return None

async def get_current_active_user(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """获取当前活跃用户"""
    if not current_user.get("is_active", True):
        logger.error(f"用户已被禁用: {current_user.get('username')}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user

async def get_current_active_superuser(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """获取当前活跃的超级用户"""
    if not current_user.get("is_superuser", False):
        logger.error(f"非超级用户尝试访问管理功能: {current_user.get('username')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级用户权限"
        )
    return current_user

async def get_current_user_ws(websocket) -> str:
    """WebSocket用户认证（FastAPI依赖注入兼容版本）"""
    user_id = await get_websocket_user(websocket)
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="WebSocket认证失败"
        )
    return user_id