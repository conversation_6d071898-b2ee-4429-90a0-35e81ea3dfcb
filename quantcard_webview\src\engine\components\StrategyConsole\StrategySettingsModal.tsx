/**
 * ⚙️ 策略设置模态框组件
 * 编辑策略基本信息、执行配置和风险设置
 */

import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useStrategyState } from '../../../store/hooks'
import type { StrategyGroup } from '../../../types/game'
import { fetchStrategyTemplate } from '../../../services/api/strategy'
import { InlineParameterEditor } from '../AdvancedParameterEditor'

// 🎨 样式组件
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`

const ModalContent = styled(motion.div)`
  background: #ffffff;
  border-radius: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

  @media (max-width: 768px) {
    max-width: 95vw;
    max-height: 95vh;
    border-radius: 16px;
  }
`

const ModalHeader = styled.div`
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
`

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
`

const CloseButton = styled(motion.button)`
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #ffffff;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    background: #e2e8f0;
    transform: scale(1.05);
  }
`

const ModalBody = styled.div`
  padding: 1.25rem;
  max-height: 70vh;
  overflow-y: auto;
`

const FormGroup = styled.div`
  margin-bottom: 1rem;
`

const Label = styled.label`
  display: block;
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.375rem;
`

const Input = styled.input`
  width: 100%;
  padding: 0.625rem 0.875rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  -webkit-appearance: none;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }

  @media (max-width: 768px) {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 0.75rem 0.875rem;
  }
`

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.625rem 0.875rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 60px;
  transition: all 0.2s ease;
  line-height: 1.4;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
`

const Select = styled.select`
  width: 100%;
  padding: 0.625rem 0.875rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
`

const ModalFooter = styled.div`
  padding: 1rem 1.25rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  background: #f8fafc;
`

const Button = styled(motion.button)<{ $variant: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.625rem 1.25rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => {
    switch (props.$variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        `
      case 'secondary':
        return `
          background: #f8fafc;
          color: #475569;
          border: 1px solid #e2e8f0;
        `
      case 'danger':
        return `
          background: #dc2626;
          color: white;
          box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
        `
      default:
        return ''
    }
  }}
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.25rem;
`

// 标签页样式
const TabBar = styled.div`
  display: flex;
  margin-bottom: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  padding: 3px;
`

const TabButton = styled(motion.button)<{ $active: boolean }>`
  flex: 1;
  padding: 0.625rem 0.875rem;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props => props.$active ? '#667eea' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#64748b'};
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    background: ${props => props.$active ? '#5a67d8' : 'rgba(102, 126, 234, 0.1)'};
  }
`

// 参数编辑区域
const ParameterSection = styled.div`
  margin-bottom: 1rem;
`

const SectionTitle = styled.h4`
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
`

const CardParameterItem = styled.div`
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
`

const CardTitle = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

// 🎮 组件属性
interface StrategySettingsModalProps {
  isOpen: boolean
  onClose: () => void
  strategyId: string | null
}

// 🎮 策略设置模态框组件
function StrategySettingsModal({ isOpen, onClose, strategyId }: StrategySettingsModalProps) {
  const { groups, updateGroup, deleteGroup } = useStrategyState()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [activeTab, setActiveTab] = useState<'basic' | 'parameters'>('basic')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    execution_mode: 'sequential' as 'sequential' | 'parallel'
  })

  // 参数编辑相关状态
  const [cardTemplates, setCardTemplates] = useState<Record<string, any>>({})
  const [cardParameters, setCardParameters] = useState<Record<string, Record<string, any>>>({})
  const [loadingTemplates, setLoadingTemplates] = useState(false)

  const strategy = strategyId ? groups.find(g => g.id === strategyId) : null
  
  // 🔄 初始化表单数据
  useEffect(() => {
    if (strategy) {
      setFormData({
        name: strategy.name,
        description: strategy.description || '',
        execution_mode: strategy.execution_mode || 'sequential'
      })

      // 加载策略卡片的模板和参数
      loadCardTemplatesAndParameters()
    }
  }, [strategy])

  // 🔄 加载策略卡片的模板和参数
  const loadCardTemplatesAndParameters = async () => {
    if (!strategy?.cards) return

    setLoadingTemplates(true)
    try {
      const templates: Record<string, any> = {}
      const parameters: Record<string, Record<string, any>> = {}

      for (const card of strategy.cards) {
        if (card.template_id) {
          try {
            const templateResponse = await fetchStrategyTemplate(card.template_id)
            templates[card.id] = templateResponse.data
            parameters[card.id] = card.parameters || {}
          } catch (error) {
            console.error(`加载模板失败 ${card.template_id}:`, error)
          }
        }
      }

      setCardTemplates(templates)
      setCardParameters(parameters)
    } catch (error) {
      console.error('加载策略模板失败:', error)
    } finally {
      setLoadingTemplates(false)
    }
  }
  
  // 📝 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!strategyId || !strategy) return

    setLoading(true)
    setError('')

    try {
      // 构建更新数据，包含参数更新
      const updateData = {
        ...formData,
        cards: strategy.cards?.map(card => ({
          ...card,
          parameters: cardParameters[card.id] || card.parameters || {}
        })) || []
      }

      await updateGroup(strategyId, updateData)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新失败')
    } finally {
      setLoading(false)
    }
  }

  // 📝 处理卡片参数变化
  const handleCardParameterChange = (cardId: string, parameters: Record<string, any>) => {
    setCardParameters(prev => ({
      ...prev,
      [cardId]: parameters
    }))
  }
  
  // 🗑️ 处理删除策略
  const handleDelete = async () => {
    if (!strategyId || !confirm('确定要删除这个策略吗？此操作不可恢复。')) return
    
    setLoading(true)
    setError('')
    
    try {
      await deleteGroup(strategyId)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除失败')
    } finally {
      setLoading(false)
    }
  }
  
  // 📝 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }
  
  if (!isOpen || !strategy) return null

  return (
    <AnimatePresence>
      <ModalOverlay
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <ModalContent
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          <ModalHeader>
            <ModalTitle>⚙️ 策略设置</ModalTitle>
            <CloseButton
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
            >
              ✕
            </CloseButton>
          </ModalHeader>
          
          <form onSubmit={handleSubmit}>
            <ModalBody>
              {/* 标签页导航 */}
              <TabBar>
                <TabButton
                  $active={activeTab === 'basic'}
                  onClick={() => setActiveTab('basic')}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  ⚙️ 基本设置
                </TabButton>
                <TabButton
                  $active={activeTab === 'parameters'}
                  onClick={() => setActiveTab('parameters')}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  🎛️ 参数配置
                </TabButton>
              </TabBar>

              {/* 基本设置标签页 */}
              {activeTab === 'basic' && (
                <>
                  <FormGroup>
                    <Label>策略名称</Label>
                    <Input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="请输入策略名称"
                      required
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label>策略描述</Label>
                    <TextArea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="请输入策略描述（可选）"
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label>执行模式</Label>
                    <Select
                      value={formData.execution_mode}
                      onChange={(e) => handleInputChange('execution_mode', e.target.value)}
                    >
                      <option value="sequential">🔄 串行执行</option>
                      <option value="parallel">⚡ 并行执行</option>
                    </Select>
                  </FormGroup>
                </>
              )}

              {/* 参数配置标签页 */}
              {activeTab === 'parameters' && (
                <ParameterSection>
                  {loadingTemplates ? (
                    <div style={{ textAlign: 'center', padding: '2rem', color: '#64748b' }}>
                      ⏳ 加载参数配置中...
                    </div>
                  ) : strategy?.cards && strategy.cards.length > 0 ? (
                    <>
                      <SectionTitle>策略卡片参数配置</SectionTitle>
                      {strategy.cards.map((card, index) => {
                        const template = cardTemplates[card.id]
                        const parameters = cardParameters[card.id] || card.parameters || {}

                        return (
                          <CardParameterItem key={card.id}>
                            <CardTitle>
                              <span>📊</span>
                              {card.name || template?.name || `策略卡片 ${index + 1}`}
                            </CardTitle>
                            {template?.parameters ? (
                              <InlineParameterEditor
                                parameters={template.parameters}
                                parameterGroups={template.parameterGroups}
                                values={parameters}
                                onChange={(values) => handleCardParameterChange(card.id, values)}
                                layout="vertical"
                                compact={true}
                                readOnly={false}
                              />
                            ) : (
                              <div style={{ color: '#64748b', fontSize: '0.8rem' }}>
                                暂无可配置参数
                              </div>
                            )}
                          </CardParameterItem>
                        )
                      })}
                    </>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '2rem', color: '#64748b' }}>
                      📊 策略组中暂无卡片
                    </div>
                  )}
                </ParameterSection>
              )}

              {error && <ErrorMessage>{error}</ErrorMessage>}
            </ModalBody>
            
            <ModalFooter>
              <Button
                type="button"
                $variant="danger"
                onClick={handleDelete}
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                🗑️ 删除策略
              </Button>
              
              <Button
                type="button"
                $variant="secondary"
                onClick={onClose}
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                取消
              </Button>
              
              <Button
                type="submit"
                $variant="primary"
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {loading ? '⏳ 保存中...' : '💾 保存'}
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </ModalOverlay>
    </AnimatePresence>
  )
}

export default StrategySettingsModal
