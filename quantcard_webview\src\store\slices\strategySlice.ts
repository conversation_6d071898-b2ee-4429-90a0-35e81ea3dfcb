/**
 * 🏗️ 策略切片 - 管理策略组和策略卡牌实例
 * 支持策略组创建、删除、更新、卡牌管理和性能跟踪
 * 使用原生immutable更新模式，保持Map数据结构的高性能
 */

import type { SliceCreator, StrategySlice } from '../types/store'
import type { StrategyGroup, StrategyCardInstance } from '../../types/game'
import {
  listStrategyGroups,
  createStrategyGroup as apiCreateStrategyGroup,
  getStrategyGroup,
  executeStrategyGroup,
  startStrategyGroup,
  stopStrategyGroup,
  updateStrategyGroup,
  deleteStrategyGroup,
  getStrategyGroupExecutionHistory
} from '../../services/api/strategy'

// 🎯 创建策略切片
export const createStrategySlice: SliceCreator<StrategySlice> = (set, get) => ({
  // 🏗️ 初始状态
  groups: [],
  currentGroupId: null,
  loading: false,
  error: undefined,

  // 📂 加载策略组
  loadGroups: async () => {
    const currentState = get()
    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 调用真实API
      const response = await listStrategyGroups(1, 50) // 获取前50个策略组

      if (!response.success) {
        throw new Error(response.message || '获取策略组列表失败')
      }

      // 直接使用后端返回的数据，无需转换
      const groups: StrategyGroup[] = response.data?.items || []

      set({
        ...get(),
        groups: groups,
        loading: false,
        error: undefined
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '策略组加载失败'
      console.error('加载策略组失败:', error)

      // 如果是token过期错误，不设置错误状态，避免重试
      if (errorMessage === 'TOKEN_EXPIRED') {
        console.log('🔄 Token过期，停止加载策略组')
        set({
          ...get(),
          loading: false,
          error: undefined // 不设置错误，避免重试
        })
        return
      }

      set({
        ...get(),
        loading: false,
        error: errorMessage
      })
    }
  },

  // 🆕 创建策略组
  createGroup: async (name: string, description: string): Promise<string> => {
    try {
      // 调用真实API创建策略组
      const response = await apiCreateStrategyGroup({
        name,
        description,
        cards: [],
        execution_mode: 'sequential',
        group_type: 'filter'
      })

      if (!response.success) {
        throw new Error(response.message || '创建策略组失败')
      }

      const newGroup = response.data
      const groupId = newGroup.id

      // 直接使用后端返回的数据格式
      const formattedGroup: StrategyGroup = {
        id: groupId,
        name: newGroup.name,
        description: newGroup.description || '',
        user_id: newGroup.user_id,
        cards: newGroup.cards || [],
        execution_mode: newGroup.execution_mode,
        status: newGroup.status,
        group_type: newGroup.group_type,
        performance_metrics: newGroup.performance_metrics,
        created_at: newGroup.created_at,
        updated_at: newGroup.updated_at
      }

      const currentState = get()
      const newGroups = [...currentState.groups, formattedGroup]

      set({
        ...currentState,
        groups: newGroups
      })

      return groupId
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建策略组失败'
      console.error('创建策略组失败:', error)
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 🗑️ 删除策略组
  deleteGroup: async (groupId: string) => {
    try {
      const currentState = get()
      const newGroups = currentState.groups.filter(group => group.id !== groupId)

      set({
        ...currentState,
        groups: newGroups,
        currentGroupId: currentState.currentGroupId === groupId ? null : currentState.currentGroupId
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除策略组失败'
      set({
        ...get(),
        error: errorMessage
      })
    }
  },

  // 🎯 设置当前策略组
  setCurrentGroup: (groupId: string | null) => {
    const currentState = get()
    set({
      ...currentState,
      currentGroupId: groupId
    })
  },

  // 🂠 添加卡牌到策略组
  addCardToGroup: async (groupId: string, cardInstance: StrategyCardInstance) => {
    try {
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)
      
      if (groupIndex === -1) {
        throw new Error('策略组不存在')
      }

      const newGroups = [...currentState.groups]
      newGroups[groupIndex] = {
        ...newGroups[groupIndex],
        cards: [...(newGroups[groupIndex].cards || []), cardInstance],
        updated_at: new Date().toISOString()
      }

      set({
        ...currentState,
        groups: newGroups
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '添加卡牌失败'
      set({
        ...get(),
        error: errorMessage
      })
    }
  },

  // 🗑️ 从策略组移除卡牌
  removeCardFromGroup: async (groupId: string, cardId: string) => {
    try {
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)
      
      if (groupIndex === -1) {
        throw new Error('策略组不存在')
      }

      const newGroups = [...currentState.groups]
      newGroups[groupIndex] = {
        ...newGroups[groupIndex],
        cards: (newGroups[groupIndex].cards || []).filter(card => card.id !== cardId),
        updated_at: new Date().toISOString()
      }

      set({
        ...currentState,
        groups: newGroups
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '移除卡牌失败'
      set({
        ...get(),
        error: errorMessage
      })
    }
  },

  // 🔧 错误处理
  clearError: () => {
    set({
      ...get(),
      error: undefined
    })
  },

  // 🎯 执行策略组
  executeGroup: async (groupId: string, mode: 'sequential' | 'parallel' = 'sequential') => {
    try {
      const response = await executeStrategyGroup(groupId, mode)

      if (!response.success) {
        throw new Error(response.message || '执行策略组失败')
      }

      // 策略执行完成，不刷新整个列表，避免loading状态
      // 只有在需要更新策略组状态时才刷新（如启动/停止择时策略）

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '执行策略组失败'
      console.error('执行策略组失败:', error)
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 🚀 启动策略组
  startGroup: async (groupId: string) => {
    try {
      const response = await startStrategyGroup(groupId)

      if (!response.success) {
        throw new Error(response.message || '启动策略组失败')
      }

      // 更新本地状态
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)

      if (groupIndex !== -1) {
        const newGroups = [...currentState.groups]
        newGroups[groupIndex] = {
          ...newGroups[groupIndex],
          status: 'active',
          updated_at: new Date().toISOString()
        }

        set({
          ...currentState,
          groups: newGroups
        })
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '启动策略组失败'
      console.error('启动策略组失败:', error)
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 🛑 停止策略组
  stopGroup: async (groupId: string) => {
    try {
      const response = await stopStrategyGroup(groupId)

      if (!response.success) {
        throw new Error(response.message || '停止策略组失败')
      }

      // 更新本地状态
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)

      if (groupIndex !== -1) {
        const newGroups = [...currentState.groups]
        newGroups[groupIndex] = {
          ...newGroups[groupIndex],
          status: 'inactive',
          updated_at: new Date().toISOString()
        }

        set({
          ...currentState,
          groups: newGroups
        })
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '停止策略组失败'
      console.error('停止策略组失败:', error)
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 📝 更新策略组
  updateGroup: async (groupId: string, data: Partial<StrategyGroup>) => {
    try {
      const response = await updateStrategyGroup(groupId, data)

      if (!response.success) {
        throw new Error(response.message || '更新策略组失败')
      }

      // 更新本地状态
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)

      if (groupIndex !== -1) {
        const newGroups = [...currentState.groups]
        newGroups[groupIndex] = {
          ...newGroups[groupIndex],
          ...data,
          updated_at: new Date().toISOString()
        }

        set({
          ...currentState,
          groups: newGroups
        })
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '更新策略组失败'
      console.error('更新策略组失败:', error)
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 📊 获取策略组执行历史
  getGroupExecutionHistory: async (groupId: string) => {
    try {
      const response = await getStrategyGroupExecutionHistory(groupId)

      if (!response.success) {
        throw new Error(response.message || '获取执行历史失败')
      }

      return response.data || []
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取执行历史失败'
      console.error('获取执行历史失败:', error)
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 🔄 重置策略状态
  resetStrategyState: () => {
    set({
      groups: [],
      currentGroupId: null,
      loading: false,
      error: undefined
    })
  }
})
