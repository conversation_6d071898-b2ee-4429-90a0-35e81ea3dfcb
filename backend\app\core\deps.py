"""
依赖注入 - 简化版，认证功能已迁移到auth.py
"""
from typing import Optional, AsyncGenerator, Dict, Any
import logging
import pandas as pd

from .runtime import StrategyRuntime
from .data.db.base import db_manager
from .config import settings
from ..services.cache_service import SimpleCacheService, cache_service
from ..services.market_data_service import MarketDataService, market_data_service
from ..services.stock_info_service import StockInfoService, stock_info_service

logger = logging.getLogger(__name__)

_STRATEGY_RUNTIME = None

async def get_strategy_runtime() -> 'StrategyRuntime':
    """获取全局策略运行时实例"""
    global _STRATEGY_RUNTIME
    if _STRATEGY_RUNTIME is None:
        from .runtime import StrategyRuntime
        _STRATEGY_RUNTIME = StrategyRuntime()
        await _STRATEGY_RUNTIME.start()
        
        # 初始化market_data_service属性，使用全局单例
        try:
            # 使用全局单例
            _STRATEGY_RUNTIME.set_market_data_service(market_data_service)
            logging.info("成功初始化MarketDataService（使用全局单例）")
        except Exception as e:
            logging.warning(f"无法设置MarketDataService，市场数据功能将不可用: {str(e)}")
            # 创建一个空的市场数据服务对象
            class DummyMarketDataService:
                async def get_realtime_quotes(self, stock_codes=None, indicators=None):
                    logging.warning("使用DummyMarketDataService，返回空DataFrame")
                    return pd.DataFrame()
            
            _STRATEGY_RUNTIME.set_market_data_service(DummyMarketDataService())
    
    return _STRATEGY_RUNTIME

# 认证相关功能已迁移到 app.core.auth 模块
# 使用: from app.core.auth import get_current_user, get_current_active_user, get_current_active_superuser

async def get_strategy_service(runtime = None):
    """获取策略服务实例"""
    if runtime is None:
        runtime = await get_strategy_runtime()
    # 在函数内部导入，避免循环引用
    from ..services.strategy_service import StrategyService
    return StrategyService(runtime)

async def get_cache_service():
    """获取缓存服务实例，使用全局单例"""
    return cache_service

async def get_market_data_service():
    """获取市场数据服务实例，使用全局单例"""
    return market_data_service

async def get_stock_info_service():
    """获取股票基础信息服务实例，使用全局单例"""
    return stock_info_service

# WebSocket认证已迁移到 app.core.auth.get_websocket_user