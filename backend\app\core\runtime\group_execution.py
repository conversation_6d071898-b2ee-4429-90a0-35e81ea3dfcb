"""
策略组执行模块

负责策略组的串行和并行执行
优化多策略的执行效率和信号处理
"""
import logging
import asyncio
from typing import List, Dict, Any
from datetime import datetime
import uuid
import time
import pytz

from .types import RuntimeContext, ExecutionResult, Signal, StrategyMode
from .execution import StrategyExecutor
from .utils import strategy_error_handler
from ...services.timing_data_manager import TimingDataManager
from ...services.in_memory_queue import in_memory_queue

logger = logging.getLogger(__name__)

# 获取当前时区的函数
def get_current_time():
    """获取当前本地时间"""
    # 使用系统设置的时区返回本地时间
    return datetime.now()

class StrategyGroupExecutor(StrategyExecutor):
    """
    策略组执行器类
    
    继承自StrategyExecutor，扩展了组策略执行功能
    支持串行和并行两种执行模式
    """
    
    def __init__(self):
        """初始化策略组执行器"""
        super().__init__()
        self.data_manager = TimingDataManager.get_instance()

    @strategy_error_handler(strategy_id="strategy_group", context_name="组执行")
    async def execute_group(self, contexts: List[RuntimeContext], mode: str = "sequential", group_type: str = "filter") -> Dict[str, Any]:
        """
        统一的策略组执行入口
        
        Args:
            contexts: 策略上下文列表
            mode: 执行方式，sequential或parallel
            group_type: 策略组类型，filter(选股)或timing(择时)
            
        Returns:
            Dict[str, Any]: 直接返回前端所需的结果格式
        """
        if not contexts:
            return {
                "success": False, 
                "message": "策略上下文列表为空", 
                "logs": [], 
                "data": {
                    "signals": [],
                    "statistics": {"total": 0},
                    "execution_time": get_current_time().strftime("%Y/%m/%d %H:%M:%S")
                }
            }
        
        # 1. 配置上下文参数
        group_id = contexts[0].metadata.get("strategy_group", {}).get("id", "")
        user_id = contexts[0].user_id
        all_logs = []
        cache_group_id = None
        
        # 发布策略组开始执行消息
        await in_memory_queue.publish("strategy_updates", {
            "type": "strategy_update",
            "update_type": "log",
            "user_id": user_id,
            "strategy_id": group_id,
            "group_id": group_id,
            "log": f"开始执行策略组: {group_type}, 模式={mode}",
            "timestamp": get_current_time().isoformat(),
            "is_public": False
        })
        
        # 设置策略组类型和执行模式
        for i, ctx in enumerate(contexts):
            ctx.metadata["group_type"] = group_type
            ctx.metadata["group_id"] = group_id
            ctx.metadata["execution_mode"] = mode
            
            # 设置策略在序列中的位置
            if i == 0:
                ctx.metadata["is_first_strategy"] = True
            else:
                ctx.metadata["is_first_strategy"] = False
            
            # 根据策略组类型设置策略模式
            if group_type == "timing":
                ctx.mode = StrategyMode.TIMING
                
                # 从元数据中获取K线周期
                group_kline_period = ctx.metadata.get("group_kline_period")
                if not group_kline_period and ctx.metadata.get("strategy_group"):
                    group_data = ctx.metadata.get("strategy_group", {})
                    group_kline_period = group_data.get("kline_period", "5min")
                    ctx.metadata["group_kline_period"] = group_kline_period
        
        # 2. 择时策略组时，设置必要的元数据
        if group_type == "timing" and group_id:
            # 获取所有标的
            all_symbols = []
            for ctx in contexts:
                if ctx.metadata.get("timing_symbols"):
                    symbols = [s.strip() for s in ctx.metadata["timing_symbols"].split(",") if s.strip()]
                    all_symbols.extend(symbols)
            
            # 去重
            all_symbols = list(set(all_symbols))[:10]  # 最多10个标的
            
            # 获取周期
            period = contexts[0].metadata.get("group_kline_period", "5min")
            
            # 生成包含周期信息的缓存组ID
            cache_group_id = f"{group_id}_{period}"
            
            # 设置缓存组ID到所有上下文元数据
            for ctx in contexts:
                ctx.metadata["cache_group_id"] = cache_group_id
                
            all_logs.append(f"准备执行策略: {len(all_symbols)} 个标的, 周期={period}")
            
            # 记录日志并发布
            await in_memory_queue.publish("strategy_updates", {
                "type": "strategy_update",
                "update_type": "log",
                "user_id": user_id,
                "strategy_id": group_id,
                "group_id": group_id,
                "log": f"准备执行策略: {len(all_symbols)} 个标的, 周期={period}",
                "timestamp": get_current_time().isoformat(),
                "is_public": False
            })
        
        # 3. 执行策略
        results = []
        all_signals = []
        
        start_time = get_current_time()
        
        try:
            if mode == "parallel" and len(contexts) > 1:
                # 并行执行
                tasks = []
                for ctx in contexts:
                    tasks.append(self._run_strategy(ctx))
                
                # 等待所有任务完成
                strategy_results = await asyncio.gather(*tasks)
                results.extend(strategy_results)
                
                # 收集所有策略的日志和信号
                for i, result in enumerate(strategy_results):
                    # 收集日志
                    if result.logs:
                        context_prefix = f"[策略{i+1}] " if len(contexts) > 1 else ""
                        all_logs.extend([f"{context_prefix}{log}" for log in result.logs])
                    
                    # 收集信号
                    if result.signals:
                        all_signals.extend(result.signals)
            else:
                # 串行执行
                for i, ctx in enumerate(contexts):
                    context_prefix = f"[策略{i+1}] " if len(contexts) > 1 else ""
                    all_logs.append(f"{context_prefix}开始执行...")
                    
                    # 发布日志
                    await in_memory_queue.publish("strategy_updates", {
                        "type": "strategy_update",
                        "update_type": "log",
                        "user_id": user_id,
                        "strategy_id": group_id,
                        "group_id": group_id,
                        "log": f"{context_prefix}开始执行...",
                        "timestamp": get_current_time().isoformat(),
                        "is_public": False
                    })
                    
                    # 如果不是第一个策略，将前一个策略产生的信号传递到下一个策略中
                    if i > 0 and all_signals:
                        ctx.metadata["previous_signals"] = all_signals
                    
                    # 执行策略
                    result = await self._run_strategy(ctx)
                    results.append(result)
                    
                    # 收集日志
                    if result.logs:
                        prefixed_logs = [f"{context_prefix}{log}" for log in result.logs]
                        all_logs.extend(prefixed_logs)
                    
                    # 收集信号
                    if result.signals:
                        # 记录信号数量
                        signal_count = len(result.signals)
                        signal_log = f"{context_prefix}生成 {signal_count} 个信号"
                        all_logs.append(signal_log)
                        
                        # 发布信号日志
                        await in_memory_queue.publish("strategy_updates", {
                            "type": "strategy_update",
                            "update_type": "log",
                            "user_id": user_id,
                            "strategy_id": group_id,
                            "group_id": group_id,
                            "log": signal_log,
                            "timestamp": get_current_time().isoformat(),
                            "is_public": False
                        })
                        
                        # 对于选股(filter)类型的串行策略组，每个策略的结果作为下一个策略的输入
                        # 每次使用当前策略的输出替换all_signals
                        if group_type == "filter":
                            # 不管是不是最后一个策略，都用当前策略的结果替换之前的信号
                            # 这样可以确保串行模式下取交集，而不是并集
                            all_signals = list(result.signals)
                        else:
                            # 非选股类型的策略组（如择时），继续累积所有信号
                            all_signals.extend(result.signals)
            
            # 判断执行结果是否成功，至少要有一个策略执行成功
            execution_success = any(r.success for r in results) if results else False
            
            # 标准化信号格式（确保所有信号字段一致）
            standardized_signals = []
            for signal in all_signals:
                # 确保信号有ID
                if not hasattr(signal, 'id') or not signal.id:
                    signal.id = f"signal-{get_current_time().timestamp()}-{uuid.uuid4()}"
                
                # 确保信号有时间戳
                if not hasattr(signal, 'timestamp') or not signal.timestamp:
                    signal.timestamp = get_current_time().isoformat()
                
                # 确保信号有策略ID和组ID
                if not hasattr(signal, 'strategy_id') or not signal.strategy_id:
                    signal.strategy_id = group_id
                
                if not hasattr(signal, 'group_id') or not signal.group_id:
                    signal.group_id = group_id
                
                standardized_signals.append(signal)
            
            # 择时策略组的信号合并逻辑
            final_signals = standardized_signals
            if group_type == "timing" and results:
                # 所有策略执行结果中的信号
                all_strategy_signals = []
                for result in results:
                    if result.success and result.signals:
                        all_strategy_signals.append(result.signals)
                
                # 如果所有策略都生成了信号
                if all_strategy_signals and all(signals for signals in all_strategy_signals):
                    # 组织信号按symbol分组
                    symbols_to_signals = {}
                    
                    # 收集所有标的
                    for signals in all_strategy_signals:
                        for signal in signals:
                            if signal.symbol not in symbols_to_signals:
                                symbols_to_signals[signal.symbol] = []
                            symbols_to_signals[signal.symbol].append(signal)
                    
                    # 应用"且"逻辑计算最终信号
                    final_signals = []
                    
                    for symbol, symbol_signals in symbols_to_signals.items():
                        # 只处理出现在所有策略中的标的
                        if len(symbol_signals) == len(all_strategy_signals):
                            # 检查所有策略对该标的的信号方向是否一致
                            directions = [s.direction for s in symbol_signals]
                            
                            # 创建基础信号（使用最后一个信号的属性）
                            base_signal = symbol_signals[-1]
                            
                            # 如果所有策略都给出BUY信号，最终结果为BUY
                            if all(d == "BUY" for d in directions):
                                final_signals.append(base_signal)
                            # 如果所有策略都给出SELL信号，最终结果为SELL
                            elif all(d == "SELL" for d in directions):
                                final_signals.append(base_signal)
                            # 其他情况（如有不一致或HOLD），输出HOLD信号
                            else:
                                # 复制最后一个信号，但修改direction为HOLD
                                hold_signal = Signal(
                                    symbol=base_signal.symbol,
                                    direction="HOLD",
                                    confidence=base_signal.confidence,
                                    type=base_signal.type,
                                    trigger_condition="策略意见不一致",
                                    name=base_signal.name,
                                    id=base_signal.id,
                                    strategy_id=base_signal.strategy_id,
                                    group_id=base_signal.group_id,
                                    timestamp=base_signal.timestamp
                                )
                                final_signals.append(hold_signal)
            
            # 将最终的信号写入all_signals
            all_signals = final_signals
            
            # 发布执行完成的消息
            await in_memory_queue.publish("strategy_updates", {
                "type": "strategy_update",
                "update_type": "log",
                "user_id": user_id,
                "strategy_id": group_id,
                "group_id": group_id,
                "log": f"策略组执行完成，耗时: {(get_current_time() - start_time).total_seconds():.2f}秒，生成 {len(all_signals)} 个信号",
                "timestamp": get_current_time().isoformat(),
                "is_public": False
            })
            
            # 如果是选股策略组且生成了信号，发送一个汇总通知
            if group_type == "filter" and all_signals:
                # 创建一个汇总信号
                summary_signal = Signal(
                    id=f"summary-{group_id}-{get_current_time().timestamp()}",
                    strategy_id=group_id,
                    group_id=group_id,
                    symbol="SUMMARY",
                    direction="FILTER",
                    type="summary",
                    confidence=1.0,
                    timestamp=get_current_time(),  # 修复：保持datetime类型，让json_encoders处理
                    metadata={
                        "total_signals": len(all_signals),
                        "filter_summary": True
                    }
                )
                
                # 推送汇总信号
                await in_memory_queue.publish("strategy_updates", {
                    "type": "strategy_update",
                    "update_type": "signal",
                    "user_id": user_id,
                    "strategy_id": group_id,
                    "group_id": group_id,
                    "signal": summary_signal.dict(exclude_none=True),
                    "timestamp": get_current_time().isoformat(),
                    "is_public": False,
                    "show_notification": True,
                    "filter_summary": True,
                    "total_signals": len(all_signals)
                })
            
            # 确保日志不为空，至少添加一条基本信息
            if not all_logs:
                all_logs = ["策略执行完成，未生成详细日志"]
                
            result_data = {
                "success": True,
                "message": "策略执行成功",
                "logs": all_logs,
                "data": {
                    "signals": [signal.dict(exclude_none=True) for signal in all_signals] if all_signals else [],
                    "statistics": {"total": len(all_signals)},
                    "execution_time": start_time.strftime("%Y/%m/%d %H:%M:%S")
                }
            }
            
            # 如果是调试模式，执行完成后清理缓存
            if contexts and contexts[0].debug and group_type == "timing" and cache_group_id:
                # 清理调试模式的缓存
                await asyncio.sleep(1)  # 等待1秒，确保结果已返回
                self.data_manager.release_group_data(cache_group_id)
                logger.info(f"调试模式执行完成，已释放缓存: {cache_group_id}")
            
            return result_data
            
        except Exception as e:
            logger.error(f"策略组执行失败: {str(e)}", exc_info=True)
            execution_time = (get_current_time() - start_time).total_seconds()
            
            # 发布执行失败的消息
            await in_memory_queue.publish("strategy_updates", {
                "type": "strategy_update",
                "update_type": "log",
                "user_id": user_id,
                "strategy_id": group_id,
                "group_id": group_id,
                "log": f"策略组执行失败: {str(e)}",
                "timestamp": get_current_time().isoformat(),
                "is_public": False
            })
            
            return {
                "success": False,
                "message": f"策略组执行失败: {str(e)}",
                "logs": all_logs + [f"错误: {str(e)}"],
                "data": {
                    "signals": [],
                    "statistics": {"total": 0},
                    "execution_time": start_time.strftime("%Y/%m/%d %H:%M:%S"),
                    "error": str(e)
                }
            }

    async def _run_strategy(self, ctx: RuntimeContext) -> ExecutionResult:
        """执行策略 - 实时推送结果"""
        start_time = get_current_time()
        
        # 推送开始执行消息
        await in_memory_queue.publish("strategy_updates", {
            "type": "strategy_update",
            "update_type": "log",
            "user_id": ctx.user_id,
            "strategy_id": ctx.strategy_id,
            "group_id": ctx.metadata.get("group_id", ""),
            "log": f"开始执行策略: {ctx.name}",
            "timestamp": get_current_time().isoformat(),
            "is_public": ctx.metadata.get("is_public", False)
        })
        
        # 调用父类的_run_strategy方法执行策略
        result = await super()._run_strategy(ctx)
        
        # 将生成的信号推送到WebSocket
        if result.signals:
            signals_count = len(result.signals)
            
            # 推送日志消息，告知生成了多少个信号
            await in_memory_queue.publish("strategy_updates", {
                "type": "strategy_update",
                "update_type": "log",
                "user_id": ctx.user_id,
                "strategy_id": ctx.strategy_id,
                "group_id": ctx.metadata.get("group_id", ""),
                "log": f"策略 {ctx.name} 生成了 {signals_count} 个信号",
                "timestamp": get_current_time().isoformat(),
                "is_public": ctx.metadata.get("is_public", False)
            })
            
            # 获取策略组类型
            group_type = ctx.metadata.get("strategy_group", {}).get("type", "")
            
            # 对于选股策略，不推送每个信号，而是在策略组执行完成后一次性推送
            # 只有择时策略才需要实时推送每个单独的信号
            if group_type != "filter":
                # 推送每个信号
                for signal in result.signals:
                    # 确保信号有必要的属性
                    if not hasattr(signal, 'id') or not signal.id:
                        signal.id = f"signal-{get_current_time().timestamp()}-{uuid.uuid4()}"
                    
                    if not hasattr(signal, 'timestamp') or not signal.timestamp:
                        signal.timestamp = get_current_time().isoformat()
                    
                    # 推送信号到WebSocket
                    await in_memory_queue.publish("strategy_updates", {
                        "type": "strategy_update",
                        "update_type": "signal",
                        "user_id": ctx.user_id,
                        "strategy_id": ctx.strategy_id,
                        "group_id": ctx.metadata.get("group_id", ""),
                        "signal": signal.dict(exclude_none=True),
                        "timestamp": get_current_time().isoformat(),
                        "is_public": ctx.metadata.get("is_public", False)
                    })
        
        return result 