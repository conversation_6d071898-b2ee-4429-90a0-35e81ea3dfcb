/**
 * 🎴 策略卡抽取场景 - Card Draw Scene
 * 提供华丽的抽卡动画和体验，支持多种卡包类型
 */

import React, { useState, useEffect, useCallback } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'
import { useAuth } from '../../store/hooks/useAuth'
import { unifiedWebSocket, MessageType } from '../../services/unifiedWebSocket'
import GameNotification from '../components/GameNotification'
import type { NotificationData } from '../components/GameNotification'

// 类型定义
interface CardPack {
  type: string
  name: string
  description: string
  icon: string
  cost: {
    type: 'free' | 'coins' | 'gems'
    amount: number
  }
  guaranteed_rarity: string
}

interface DrawnCard {
  template_id: string
  name: string
  description: string
  rarity: string
  icon: string
  color: string
  is_new: boolean
}

// 样式组件
const Container = styled.div`
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1e1b4b 0%, #312e81 25%, #3730a3 50%, #1e40af 75%, #1e3a8a 100%);
  overflow: hidden;
  position: relative;
`

const Content = styled.div`
  padding: 1rem;
  height: calc(100vh - 140px);
  overflow-y: auto;
  position: relative;
`

const PacksGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

const PackCard = styled(motion.div)<{ $canAfford: boolean }>`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  cursor: ${props => props.$canAfford ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.$canAfford ? 1 : 0.6};
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #f59e0b);
  }
`

const PackIcon = styled.div`
  font-size: 4rem;
  text-align: center;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
`

const PackName = styled.h3`
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 0.5rem;
`

const PackDescription = styled.p`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 1.5rem;
`

const PackCost = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  
  .cost-type {
    font-size: 1.2rem;
  }
  
  .cost-amount {
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
  }
`

const DrawButton = styled(motion.button)<{ $canAfford: boolean }>`
  width: 100%;
  padding: 1rem;
  background: ${props => props.$canAfford 
    ? 'linear-gradient(135deg, #3b82f6, #8b5cf6)' 
    : 'rgba(255, 255, 255, 0.1)'};
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: ${props => props.$canAfford ? 'pointer' : 'not-allowed'};
  transition: all 0.3s ease;
  
  &:hover {
    ${props => props.$canAfford && `
      background: linear-gradient(135deg, #2563eb, #7c3aed);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    `}
  }
`

const CardRevealOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`

const CardRevealContainer = styled(motion.div)`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  max-width: 400px;
  width: 100%;
  backdrop-filter: blur(20px);
`

const RevealedCard = styled(motion.div)<{ $rarity: string }>`
  background: ${props => {
    const rarityColors = {
      common: 'linear-gradient(135deg, #6b7280, #9ca3af)',
      rare: 'linear-gradient(135deg, #3b82f6, #60a5fa)',
      epic: 'linear-gradient(135deg, #8b5cf6, #a78bfa)',
      legendary: 'linear-gradient(135deg, #f59e0b, #fbbf24)',
      mythic: 'linear-gradient(135deg, #ef4444, #f87171)'
    }
    return rarityColors[props.$rarity as keyof typeof rarityColors] || rarityColors.common
  }};
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  color: white;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
`

const CardIcon = styled.div`
  font-size: 6rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
`

const CardName = styled.h2`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`

const CardRarity = styled.div`
  font-size: 1rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 1rem;
`

const CardDescription = styled.p`
  font-size: 0.9rem;
  opacity: 0.8;
  line-height: 1.4;
`

const CloseButton = styled(motion.button)`
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
`

const LoadingSpinner = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  
  .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    margin-bottom: 1rem;
  }
  
  .text {
    font-size: 1.2rem;
    font-weight: 600;
  }
`

const StarsBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.6), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.4), transparent),
      radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.6), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: twinkle 4s ease-in-out infinite alternate;
  }
  
  @keyframes twinkle {
    0% { opacity: 0.3; }
    100% { opacity: 0.8; }
  }
`

export default function CardDrawScene() {
  const { isAuthenticated, isGuest } = useAuth()
  const [packs, setPacks] = useState<CardPack[]>([])
  const [loading, setLoading] = useState(false)
  const [revealedCard, setRevealedCard] = useState<DrawnCard | null>(null)
  const [notifications, setNotifications] = useState<NotificationData[]>([])

  // 加载卡包信息
  useEffect(() => {
    const loadPacks = async () => {
      try {
        // 使用统一认证工具获取请求头
        const { AuthTokenManager } = await import('../../utils/authUtils')
        const headers = AuthTokenManager.getAuthHeaders()

        const response = await fetch('/api/cards/packs', {
          headers
        })
        
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            setPacks(result.data.packs)
          }
        }
      } catch (error) {
        console.error('加载卡包信息失败:', error)
      }
    }

    if (isAuthenticated && !isGuest) {
      loadPacks()
    }
  }, [isAuthenticated, isGuest])

  // 添加通知
  const addNotification = useCallback((notification: Omit<NotificationData, 'id'>) => {
    const newNotification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
    }
    setNotifications(prev => [...prev, newNotification])
  }, [])

  // 移除通知
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }, [])

  // 抽卡操作
  const handleDrawCard = async (pack: CardPack) => {
    if (!isAuthenticated || isGuest) {
      addNotification({
        type: 'warning',
        title: '需要登录',
        message: '请先登录才能抽卡',
        icon: '🔐'
      })
      return
    }

    try {
      setLoading(true)

      // 通过WebSocket发送抽卡请求
      const result = await unifiedWebSocket.sendMessage({
        type: MessageType.INVENTORY,
        subtype: 'draw_card',
        payload: {
          count: 1,
          pack_type: pack.type
        }
      })

      if (result && result.success && result.data) {
        const drawnCards = result.data.drawn_cards || []
        
        if (drawnCards.length > 0) {
          const card = drawnCards[0]
          setRevealedCard(card)
          
          addNotification({
            type: 'success',
            title: '抽卡成功！',
            message: `获得【${card.name}】`,
            icon: card.icon || '🎴',
            duration: 3000
          })
        }
      } else {
        throw new Error(result?.message || '抽卡失败')
      }
    } catch (error) {
      console.error('抽卡失败:', error)
      addNotification({
        type: 'error',
        title: '抽卡失败',
        message: error instanceof Error ? error.message : '网络错误',
        icon: '❌'
      })
    } finally {
      setLoading(false)
    }
  }

  const canAffordPack = (pack: CardPack) => {
    // 这里可以根据用户的货币情况判断是否能购买
    // 暂时都返回true，表示可以抽取
    return pack.cost.type === 'free' || true
  }

  const rarityDisplayNames = {
    common: '普通',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythic: '神话'
  }

  if (!isAuthenticated) {
    return (
      <Container>
        <StarsBackground />
        <UnifiedMobileNav title="策略抽卡" showBackButton={true}>
          <Content>
            <div style={{ 
              textAlign: 'center', 
              color: 'white', 
              marginTop: '3rem',
              position: 'relative',
              zIndex: 1
            }}>
              <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🎴</div>
              <h2>需要登录才能抽卡</h2>
              <p>登录后可以获取各种稀有的策略卡牌</p>
            </div>
          </Content>
        </UnifiedMobileNav>
      </Container>
    )
  }

  if (isGuest) {
    return (
      <Container>
        <StarsBackground />
        <UnifiedMobileNav title="策略抽卡" showBackButton={true}>
          <Content>
            <div style={{ 
              textAlign: 'center', 
              color: 'white', 
              marginTop: '3rem',
              position: 'relative',
              zIndex: 1
            }}>
              <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🎴</div>
              <h2>访客模式下无法抽卡</h2>
              <p>注册账号后可以体验完整的抽卡功能</p>
            </div>
          </Content>
        </UnifiedMobileNav>
      </Container>
    )
  }

  return (
    <Container>
      <StarsBackground />
      <UnifiedMobileNav title="策略抽卡" showBackButton={true}>
        <Content style={{ position: 'relative', zIndex: 1 }}>
          <PacksGrid>
            {packs.map((pack) => (
              <PackCard
                key={pack.type}
                $canAfford={canAffordPack(pack)}
                onClick={() => canAffordPack(pack) && handleDrawCard(pack)}
                whileHover={{ scale: canAffordPack(pack) ? 1.02 : 1, y: canAffordPack(pack) ? -4 : 0 }}
                whileTap={{ scale: canAffordPack(pack) ? 0.98 : 1 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <PackIcon>{pack.icon}</PackIcon>
                <PackName>{pack.name}</PackName>
                <PackDescription>{pack.description}</PackDescription>
                
                <PackCost>
                  <span className="cost-type">
                    {pack.cost.type === 'free' ? '🆓' : 
                     pack.cost.type === 'coins' ? '🪙' : '💎'}
                  </span>
                  <span className="cost-amount">
                    {pack.cost.type === 'free' ? '免费' : pack.cost.amount}
                  </span>
                </PackCost>
                
                <DrawButton
                  $canAfford={canAffordPack(pack)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {pack.cost.type === 'free' ? '免费抽取' : '立即抽取'}
                </DrawButton>
              </PackCard>
            ))}
          </PacksGrid>
        </Content>
      </UnifiedMobileNav>

      {/* 加载动画 */}
      <AnimatePresence>
        {loading && (
          <LoadingSpinner
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="spinner"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <div className="text">正在抽取策略卡牌...</div>
          </LoadingSpinner>
        )}
      </AnimatePresence>

      {/* 卡牌揭示动画 */}
      <AnimatePresence>
        {revealedCard && (
          <CardRevealOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setRevealedCard(null)}
          >
            <CardRevealContainer
              initial={{ scale: 0.8, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 50 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              <RevealedCard
                $rarity={revealedCard.rarity}
                initial={{ rotateY: 180 }}
                animate={{ rotateY: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <CardIcon>{revealedCard.icon}</CardIcon>
                <CardName>{revealedCard.name}</CardName>
                <CardRarity>
                  {rarityDisplayNames[revealedCard.rarity as keyof typeof rarityDisplayNames] || revealedCard.rarity}
                </CardRarity>
                <CardDescription>{revealedCard.description}</CardDescription>
              </RevealedCard>
              
              <CloseButton
                onClick={() => setRevealedCard(null)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                收入卡包 ✨
              </CloseButton>
            </CardRevealContainer>
          </CardRevealOverlay>
        )}
      </AnimatePresence>

      {/* 通知系统 */}
      <GameNotification
        notifications={notifications}
        onDismiss={removeNotification}
      />
    </Container>
  )
} 