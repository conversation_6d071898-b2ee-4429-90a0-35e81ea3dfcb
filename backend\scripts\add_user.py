#!/usr/bin/env python
"""
添加用户脚本

用法:
    python -m backend.scripts.add_user --username <用户名> --password <密码> [--email <邮箱>] [--superuser]

示例:
    python -m backend.scripts.add_user --username admin --password admin123 --email <EMAIL> --superuser
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from backend.app.core.auth import get_password_hash
from backend.app.models.user import User
from backend.app.core.data.db.base import db_manager
from datetime import datetime
from bson import ObjectId

async def add_user(username: str, password: str, email: str = None, is_superuser: bool = False):
    """添加用户到数据库"""
    try:
        # 检查用户名是否已存在
        db = await db_manager.get_mongodb_database("quantcard")
        existing_user = await db.users.find_one({"username": username})
        if existing_user:
            print(f"错误: 用户名 '{username}' 已存在")
            return False
        
        # 创建用户数据
        user_data = {
            "username": username,
            "hashed_password": get_password_hash(password),
            "is_active": True,
            "is_superuser": is_superuser,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "favorites": []
        }
        
        if email:
            user_data["email"] = email
        
        # 插入用户数据
        result = await db.users.insert_one(user_data)
        
        if result.inserted_id:
            user_id = str(result.inserted_id)
            print(f"成功: 用户 '{username}' 已创建，ID: {user_id}")
            print(f"用户类型: {'超级管理员' if is_superuser else '普通用户'}")
            return True
        else:
            print("错误: 创建用户失败")
            return False
            
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="添加用户到QuantCard系统")
    parser.add_argument("--username", required=True, help="用户名")
    parser.add_argument("--password", required=True, help="密码")
    parser.add_argument("--email", help="电子邮箱")
    parser.add_argument("--superuser", action="store_true", help="是否为超级管理员")
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_args()
    
    # 确认信息
    print("\n用户信息:")
    print(f"用户名: {args.username}")
    print(f"邮箱: {args.email if args.email else '无'}")
    print(f"超级管理员: {'是' if args.superuser else '否'}")
    
    confirm = input("\n确认创建用户? (y/n): ")
    if confirm.lower() != 'y':
        print("已取消")
        return
    
    # 添加用户
    await add_user(
        username=args.username,
        password=args.password,
        email=args.email,
        is_superuser=args.superuser
    )

if __name__ == "__main__":
    asyncio.run(main()) 