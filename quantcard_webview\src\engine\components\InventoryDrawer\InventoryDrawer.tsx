/**
 * 📱 抽屉式背包面�?- Inventory Drawer
 * 从底部向上滑出的背包界面，占据约80%屏幕高度
 */

import React, { useState, useMemo, useCallback, useEffect } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { UniversalCard, createCardForScene } from '../UniversalCard'
import type { UnifiedCardData } from '../UniversalCard'
import type { StrategyTemplate } from '../../../types/game'
import { useCardsState } from '../../../store/hooks/useCardsState'
import { useAuth } from '../../../store/hooks/useAuth'
import { unifiedWebSocket } from '../../../services/unifiedWebSocket'
import { templateService } from '../../../services/templateService'

// 🎭 抽屉容器
const DrawerOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(4px);
`

const DrawerContainer = styled(motion.div)`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80vh;
  background: linear-gradient(135deg, #3b2f8b 0%, #6b46c1 25%, #7c3aed 50%, #8b5cf6 75%, #a855f7 100%);
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 32px rgba(139, 92, 246, 0.3);
  overflow: hidden;
  z-index: 1001;
  display: flex;
  flex-direction: column;
`

// 🎯 抽屉头部
const DrawerHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
`

const DrawerTitle = styled.h2`
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &::before {
    content: '🎒';
    font-size: 1.5rem;
  }
`

const HeaderStats = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`

const StatBadge = styled.div<{ $color: string }>`
  background: ${props => props.$color}20;
  border: 1px solid ${props => props.$color};
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
`

const CloseButton = styled(motion.button)`
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.25rem;
  font-weight: bold;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
`

// 🔍 筛选和搜索区域
const FilterSection = styled.div`
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
`

const FilterTabs = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  
  &::-webkit-scrollbar {
    display: none;
  }
`

const FilterTab = styled(motion.button)<{ $active: boolean }>`
  background: ${props => props.$active 
    ? 'rgba(255, 255, 255, 0.3)' 
    : 'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => props.$active 
    ? 'rgba(255, 255, 255, 0.5)' 
    : 'rgba(255, 255, 255, 0.2)'
  };
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }
`

const SearchInput = styled.input`
  width: 100%;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: #ffffff;
  font-size: 1rem;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }
  
  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
  }
`

// 📦 卡牌网格区域
const InventoryContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem 1.5rem 1rem;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`

const InventoryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 6px;
  justify-content: center;
  
  /* 移动端：更紧凑的布局 */
  @media (max-width: 640px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
  
  /* 平板端：4列布局 */
  @media (min-width: 641px) and (max-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
  }
  
  /* 桌面端：更紧凑的网格 */
  @media (min-width: 1025px) {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }
`

const EmptyState = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  
  .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
  }
  
  .title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .description {
    color: rgba(255, 255, 255, 0.6);
    max-width: 300px;
    font-size: 0.9rem;
  }
`

// 加载与错误提示
const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 10, 15, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  z-index: 1002;
`

const LoadingSpinner = styled(motion.div)`
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #ffffff;
  border-radius: 50%;
`

const ErrorToast = styled(motion.div)`
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background: rgba(239, 68, 68, 0.9);
  color: #ffffff;
  padding: 1rem;
  border-radius: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1003;
`

// 🃏 卡片包装与数量角�?用尽遮罩
const CardWrapper = styled(motion.div)<{ $available: boolean }>`
  position: relative;
  opacity: ${props => props.$available ? 1 : 0.6};
  cursor: ${props => props.$available ? 'pointer' : 'not-allowed'};
  
  ${props => !props.$available && `
    &::after {
      content: '已用尽';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(239, 68, 68, 0.9);
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 8px;
      font-size: 0.7rem;
      font-weight: 600;
      z-index: 10;
    }
  `}
`

const QuantityBadge = styled.div<{ $count: number }>`
  position: absolute;
  top: -8px;
  right: -8px;
  background: ${props => props.$count > 0 ? '#10b981' : '#ef4444'};
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  border: 2px solid #3b2f8b;
  z-index: 5;
`

// 🃏 卡牌详情模态框
const CardDetailModal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`

const CardDetailContent = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  max-width: 90vw;
  max-height: 90vh;
  width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  position: relative;
`

const CardDetailHeader = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`

const CardDetailTitle = styled.h3`
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
`

const DetailCloseButton = styled(motion.button)`
  background: rgba(0, 0, 0, 0.1);
  border: none;
  color: #64748b;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #1e293b;
    transform: scale(1.1);
  }
`

const CardDetailBody = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
`

const CardDetailDescription = styled.div`
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.6;
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  width: 100%;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
`

const CardDetailActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
`

const ActionButton = styled(motion.button)<{ $variant: 'primary' | 'secondary' | 'danger' }>`
  background: ${props => {
    switch(props.$variant) {
      case 'primary': return 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
      case 'secondary': return 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
      case 'danger': return 'rgba(0, 0, 0, 0.05)'
    }
  }};
  border: ${props => props.$variant === 'danger' ? '1px solid rgba(0, 0, 0, 0.1)' : 'none'};
  color: ${props => props.$variant === 'danger' ? '#64748b' : '#ffffff'};
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => {
      switch(props.$variant) {
        case 'primary': return '0 8px 25px rgba(59, 130, 246, 0.4)'
        case 'secondary': return '0 8px 25px rgba(139, 92, 246, 0.4)'
        case 'danger': return '0 4px 12px rgba(0, 0, 0, 0.1)'
      }
    }};
  }
  
  &:active {
    transform: translateY(0);
  }
`

// 🎴 收藏物品接口
interface CollectionItem extends StrategyTemplate {
  quantity: number
  acquired_at: Date
  total_acquired: number
  source: 'activity' | 'purchase' | 'reward' | 'achievement'
  level: number
  experience: number
  power: number
  defense: number
  speed: number
  cost: number
}

// 📋 筛选类型（扩展了可用性与稀有度）
type FilterType = 'all' | 'filter' | 'timing' | 'risk_management' | 'favorites' | 'available' | 'used' | 'common' | 'rare' | 'epic' | 'legendary' | 'mythic'

// 🎭 组件属性
interface InventoryDrawerProps {
  isOpen: boolean
  onClose: () => void
  onCardSelect?: (card: UnifiedCardData) => void
  onUseCard?: (card: UnifiedCardData) => void
  onViewCardLibrary?: (card: UnifiedCardData) => void
}

/**
 * 📱 抽屉式背包组�? */
export const InventoryDrawer: React.FC<InventoryDrawerProps> = ({
  isOpen,
  onClose,
  onCardSelect,
  onUseCard,
  onViewCardLibrary
}) => {
  const [activeFilter, setActiveFilter] = useState<FilterType>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCards, setSelectedCards] = useState<Set<string>>(new Set())
  const [detailCard, setDetailCard] = useState<CollectionItem | null>(null)
  const [templateCache, setTemplateCache] = useState<Record<string, any>>({})

  // ✅ 使用真实库存和认证
  const { inventory, loading, error, loadInventory, clearError } = useCardsState()
  const { isAuthenticated, isGuest, token } = useAuth()

  // 初次打开时检查权限并加载库存
  useEffect(() => {
    if (isOpen) {
      if (!isAuthenticated) {
        // 未认证用户无法查看库存
        return
      }
      
      if (isGuest) {
        // 访客用户显示提示
        return
      }
      
      // 每次打开都重新加载库存，确保数据最新
      loadInventory()
    }
  }, [isOpen, isAuthenticated, isGuest, loadInventory])

  // 监听库存变化，自动重新加载
  useEffect(() => {
    if (!isOpen || !isAuthenticated || isGuest) return

    // 监听WebSocket库存更新消息
    const unsubscribe = unifiedWebSocket.subscribe('inventory', (message) => {
      if (message.subtype === 'update') {
        console.log('收到库存更新消息，重新加载库存')
        loadInventory()
      }
    })

    return unsubscribe
  }, [isOpen, isAuthenticated, isGuest, loadInventory])

  // 🔄 加载模板数据 - 确保认证状态已同步
  useEffect(() => {
    if (!isOpen || inventory.length === 0 || !isAuthenticated) return

    const loadTemplateData = async () => {
      const templateIds = inventory.map(item => item.template_id)

      try {
        console.log('🔍 InventoryDrawer 加载模板数据，认证状态:', { isAuthenticated, templateIds })
        const templates = await templateService.getTemplates(templateIds)
        setTemplateCache(templates)
      } catch (error) {
        console.error('加载模板数据失败:', error)
      }
    }

    loadTemplateData()
  }, [isOpen, inventory, isAuthenticated])

  // 统计信息（迁移自 MobileInventoryPanel）
  const stats = useMemo(() => {
    const totalCards = inventory.reduce((sum, it) => sum + (it.quantity || 0), 0)
    const capacity = inventory.length
    const usedSlots = inventory.filter(it => (it.quantity || 0) === 0).length
    return { totalCards, usedSlots, capacity }
  }, [inventory])

  // 将库存项映射为统一卡牌数据 - 使用模板服务
  const collectionItems = useMemo<CollectionItem[]>(() => {
    return inventory.map(it => {
      const template = templateCache[it.template_id]
      const fallbackName = it.template_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

      return {
        id: it.template_id,
        name: template?.name || fallbackName,
        description: template?.description || '策略卡牌',
        category: template?.category || 'timing',
        rarity: template?.rarity || 'common',
        parameters: template?.parameters || {},
        icon: template?.icon || '🃏',
        tags: template?.tags || it.template_id.split('_'),
        quantity: it.quantity,
        acquired_at: new Date(it.acquired_at || Date.now()),
        total_acquired: it.total_acquired || it.quantity,
        source: (it as any).source || 'activity',
        level: 1,
        experience: 0,
        coreSentence: template?.coreSentence || template?.description || '',
      }
    })
  }, [inventory, templateCache])

  // 🔍 筛选逻辑（扩展可用性与稀有度）
  const filteredItems = useMemo(() => {
    let filtered = collectionItems

    if (activeFilter !== 'all' && activeFilter !== 'favorites' && activeFilter !== 'filter' && activeFilter !== 'timing' && activeFilter !== 'risk_management') {
      if (activeFilter === 'available') {
        filtered = filtered.filter(item => item.quantity > 0)
      } else if (activeFilter === 'used') {
        filtered = filtered.filter(item => item.quantity === 0)
      } else {
        // 稀有度筛选
        filtered = filtered.filter(item => item.rarity === activeFilter)
      }
    } else if (activeFilter === 'favorites') {
      filtered = filtered.filter(item => item.rarity === 'legendary' || item.rarity === 'mythic')
    } else if (activeFilter === 'timing' || activeFilter === 'risk_management' || activeFilter === 'filter') {
      filtered = filtered.filter(item => item.category === activeFilter)
    }

    if (searchQuery) {
      const q = searchQuery.toLowerCase()
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(q) ||
        item.description.toLowerCase().includes(q) ||
        item.id.toLowerCase().includes(q)
      )
    }

    return filtered
  }, [collectionItems, activeFilter, searchQuery])

  // 🎯 处理卡牌点击（支持选中与回调）
  const handleCardClick = useCallback((card: UnifiedCardData) => {
    const cardItem = collectionItems.find(item => item.id === card.id)
    if (cardItem) {
      setDetailCard(cardItem)
    }
    onCardSelect?.(card)
  }, [onCardSelect, collectionItems])

  // 🎯 处理模态框操作 - 直接使用卡片（不消费）
  const handleUseCard = useCallback(() => {
    if (!detailCard) return

    if (!isAuthenticated || isGuest) {
      // 权限检查
      return
    }

    // 直接调用使用回调，不消费卡片
    onUseCard?.(detailCard as any)
    setDetailCard(null)
  }, [detailCard, onUseCard, isAuthenticated, isGuest])

  const handleViewLibrary = useCallback(() => {
    if (detailCard) {
      onViewCardLibrary?.(detailCard as any)
      setDetailCard(null)
    }
  }, [detailCard, onViewCardLibrary])

  const handleCloseDetail = useCallback(() => {
    setDetailCard(null)
  }, [])

  // 🎭 抽屉动画配置
  const drawerVariants = {
    hidden: {
      y: 500,
      opacity: 0,
      transition: {
        type: 'spring' as const,
        damping: 30,
        stiffness: 300
      }
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        damping: 30,
        stiffness: 300
      }
    }
  }

  const overlayVariants = {
    hidden: {
      opacity: 0 as const,
      transition: {
        duration: 0.2
      }
    },
    visible: {
      opacity: 1 as const,
      transition: {
        duration: 0.3
      }
    }
  }

  // 阻止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <DrawerOverlay
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            onClick={onClose}
          />
          
          {/* 抽屉容器 */}
          <DrawerContainer
            variants={drawerVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 抽屉头部 */}
            <DrawerHeader>
              <DrawerTitle>策略背包</DrawerTitle>
              <HeaderStats>
                <StatBadge $color="#10b981">{stats.totalCards}</StatBadge>
                <StatBadge $color="#f59e0b">{stats.usedSlots}/{stats.capacity}</StatBadge>
              </HeaderStats>
              <CloseButton
                onClick={onClose}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                ×
              </CloseButton>
            </DrawerHeader>

            {/* 筛选和搜索区域 */}
            <FilterSection>
              <FilterTabs>
                {[
                  { key: 'all', label: '全部', icon: '📦' },
                  { key: 'available', label: '可用', icon: '✅' },
                  { key: 'used', label: '已用', icon: '🕳️' },
                  { key: 'common', label: '普通', icon: '⚪' },
                  { key: 'rare', label: '稀有', icon: '🔵' },
                  { key: 'epic', label: '史诗', icon: '🟣' },
                  { key: 'legendary', label: '传说', icon: '🟠' },
                  { key: 'mythic', label: '神话', icon: '🔴' }
                ].map(({ key, label, icon }) => (
                  <FilterTab
                    key={key}
                    $active={activeFilter === (key as FilterType)}
                    onClick={() => setActiveFilter(key as FilterType)}
                    whileTap={{ scale: 0.95 }}
                  >
                    {icon} {label}
                  </FilterTab>
                ))}
              </FilterTabs>
              
              <SearchInput
                placeholder="🔍 搜索策略卡牌..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </FilterSection>

            {/* 背包内容区域 */}
            <div style={{ position: 'relative', flex: 1, display: 'flex', flexDirection: 'column' }}>
              <AnimatePresence>
                {loading && (
                  <LoadingOverlay>
                    <LoadingSpinner
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    />
                  </LoadingOverlay>
                )}
              </AnimatePresence>

                          <InventoryContent>
              {!isAuthenticated ? (
                <EmptyState
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="icon">🔐</div>
                  <div className="title">需要登录</div>
                  <div className="description">
                    请先登录查看你的策略卡牌背包
                  </div>
                </EmptyState>
              ) : isGuest ? (
                <EmptyState
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="icon">👤</div>
                  <div className="title">访客模式</div>
                  <div className="description">
                    访客无法查看背包，请注册正式账号体验完整功能
                  </div>
                </EmptyState>
              ) : filteredItems.length > 0 ? (
                  <InventoryGrid>
                    <AnimatePresence mode="popLayout">
                      {filteredItems.map((item, index) => (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, scale: 0.8, y: 20 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          exit={{ opacity: 0, scale: 0.8, y: -20 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          layout
                        >
                          <CardWrapper $available={item.quantity > 0}>
                            <UniversalCard
                              card={item as any}
                              {...createCardForScene(item as any, 'inventory')}
                              variant="inventory"
                              onCardClick={handleCardClick}
                              showLevel={true}
                              showStats={true}
                              size="sm"
                              selectable={false}
                              selected={false}
                              interactive={item.quantity > 0}
                              disabled={item.quantity === 0}
                            />
                            <QuantityBadge $count={item.quantity}>
                              {item.quantity}
                            </QuantityBadge>
                          </CardWrapper>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </InventoryGrid>
                ) : (
                  <EmptyState
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="icon">🕳️</div>
                    <div className="title">空空如也</div>
                    <div className="description">
                      {searchQuery ? '没有找到匹配的策略卡牌' : '背包中还没有策略卡牌，快去冒险获取吧！'}
                    </div>
                  </EmptyState>
                )}
              </InventoryContent>

              {/* 错误提示 */}
              <AnimatePresence>
                {error && (
                  <ErrorToast
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 50 }}
                  >
                    <span>{error}</span>
                    <button
                      onClick={clearError}
                      style={{
                        background: 'transparent',
                        border: 'none',
                        color: 'white',
                        fontSize: '1.2rem',
                        cursor: 'pointer'
                      }}
                    >
                      ×
                    </button>
                  </ErrorToast>
                )}
              </AnimatePresence>
            </div>
          </DrawerContainer>

          {/* 🃏 卡牌详情模态框 */}
          <AnimatePresence>
            {detailCard && (
              <CardDetailModal
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                onClick={handleCloseDetail}
              >
                <CardDetailContent
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                  transition={{ duration: 0.3, ease: 'easeOut' }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <CardDetailHeader>
                    <CardDetailTitle>{detailCard.name}</CardDetailTitle>
                    <DetailCloseButton
                      onClick={handleCloseDetail}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      ×
                    </DetailCloseButton>
                  </CardDetailHeader>

                  <CardDetailBody>
                    {/* 卡牌预览 */}
                    <UniversalCard
                      card={detailCard as any}
                      variant="collection"
                      size="lg"
                      interactive={false}
                      showLevel={true}
                      showStats={true}
                    />

                    {/* 卡牌描述 */}
                    <CardDetailDescription>
                      {detailCard.description || `这是一张 ${detailCard.name} 策略卡牌，可以用于构建强大的交易策略。该卡牌具有独特的属性和效果，能够帮助您在市场中获得优势。`}
                    </CardDetailDescription>

                    {/* 操作按钮 */}
                    <CardDetailActions>
                      <ActionButton
                        $variant="primary"
                        onClick={handleUseCard}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        disabled={detailCard.quantity === 0}
                      >
                        🎯 使用卡片
                      </ActionButton>
                      
                      <ActionButton
                        $variant="secondary"
                        onClick={handleViewLibrary}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        📚 进入图鉴
                      </ActionButton>
                      
                      <ActionButton
                        $variant="danger"
                        onClick={handleCloseDetail}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        关闭
                      </ActionButton>
                    </CardDetailActions>
                  </CardDetailBody>
                </CardDetailContent>
              </CardDetailModal>
            )}
          </AnimatePresence>
        </>
      )}
    </AnimatePresence>
  )
}

export default InventoryDrawer
