/**
 * 统一认证工具类
 * 整合所有前端认证相关功能，消除重复代码
 */

export interface AuthData {
  token: string
  user: any
  timestamp: number
}

export interface AuthHeaders {
  'Content-Type': string
  'Authorization'?: string
  [key: string]: string
}

/**
 * 统一认证Token管理器
 */
export class AuthTokenManager {
  private static readonly STORAGE_KEYS = {
    AUTH: 'quantcard_auth',
    GUEST_AUTH: 'quantcard_guest_auth'
  } as const

  /**
   * 统一获取认证Token - 简化版本，直接从存储获取
   */
  static getToken(): string | null {
    try {
      console.log('🔍 开始获取认证Token...')

      // 优先从localStorage获取（记住我）
      const localAuth = localStorage.getItem(this.STORAGE_KEYS.AUTH)
      if (localAuth) {
        const authData = JSON.parse(localAuth) as AuthData
        if (authData.token) {
          console.log('🔑 从localStorage获取token成功:', authData.token.substring(0, 50) + '...')
          return authData.token
        }
      }

      // 从sessionStorage获取（普通登录）
      const sessionAuth = sessionStorage.getItem(this.STORAGE_KEYS.AUTH)
      if (sessionAuth) {
        const authData = JSON.parse(sessionAuth) as AuthData
        if (authData.token) {
          console.log('🔑 从sessionStorage获取token成功:', authData.token.substring(0, 50) + '...')
          return authData.token
        }
      }

      // 从sessionStorage获取访客Token
      const guestAuth = sessionStorage.getItem(this.STORAGE_KEYS.GUEST_AUTH)
      if (guestAuth) {
        const authData = JSON.parse(guestAuth) as AuthData
        if (authData.token) {
          console.log('🔑 从访客存储获取token成功:', authData.token.substring(0, 50) + '...')
          return authData.token
        }
      }

      console.log('❌ 未找到有效token')
      return null
    } catch (error) {
      console.error('获取Token失败:', error)
      return null
    }
  }

  /**
   * 统一生成认证请求头
   */
  static getAuthHeaders(extraHeaders: Record<string, string> = {}): AuthHeaders {
    const token = this.getToken()
    const headers: AuthHeaders = {
      'Content-Type': 'application/json',
      ...extraHeaders
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    return headers
  }

  /**
   * 清理所有认证数据
   */
  static clearAllAuthData(): void {
    localStorage.removeItem(this.STORAGE_KEYS.AUTH)
    sessionStorage.removeItem(this.STORAGE_KEYS.AUTH)
    sessionStorage.removeItem(this.STORAGE_KEYS.GUEST_AUTH)
  }

  /**
   * 清理过期的认证数据 - 暂时禁用，避免干扰新token
   */
  static cleanupExpiredAuth(): void {
    // 暂时禁用自动清理，让后端来验证token有效性
    // 这样可以避免前端错误地清理仍然有效的token
    console.log('🔄 跳过自动清理过期认证数据，让后端验证token有效性')
  }

  /**
   * 保存认证数据 - 修复token缓存问题
   */
  static saveAuthData(token: string, user: any, remember: boolean = false, isGuest: boolean = false): void {
    const authData: AuthData = {
      token,
      user,
      timestamp: Date.now()
    }

    try {
      // 先清理所有旧的认证数据，避免token冲突
      this.clearAllAuthData()

      // 然后保存新的认证数据
      if (isGuest) {
        sessionStorage.setItem(this.STORAGE_KEYS.GUEST_AUTH, JSON.stringify(authData))
        console.log('✅ 访客认证数据已保存')
      } else if (remember) {
        localStorage.setItem(this.STORAGE_KEYS.AUTH, JSON.stringify(authData))
        console.log('✅ 记住我认证数据已保存到localStorage')
      } else {
        sessionStorage.setItem(this.STORAGE_KEYS.AUTH, JSON.stringify(authData))
        console.log('✅ 会话认证数据已保存到sessionStorage')
      }
    } catch (error) {
      console.error('保存认证数据失败:', error)
    }
  }

  /**
   * 获取当前用户信息 - 修复过期检查逻辑
   */
  static getCurrentUser(): any | null {
    try {
      // 优先从localStorage获取
      const localAuth = localStorage.getItem(this.STORAGE_KEYS.AUTH)
      if (localAuth) {
        const authData = JSON.parse(localAuth) as AuthData
        if (authData.user) {
          return authData.user
        }
      }

      // 从sessionStorage获取
      const sessionAuth = sessionStorage.getItem(this.STORAGE_KEYS.AUTH)
      if (sessionAuth) {
        const authData = JSON.parse(sessionAuth) as AuthData
        if (authData.user) {
          return authData.user
        }
      }

      // 从访客认证获取
      const guestAuth = sessionStorage.getItem(this.STORAGE_KEYS.GUEST_AUTH)
      if (guestAuth) {
        const authData = JSON.parse(guestAuth) as AuthData
        if (authData.user) {
          return authData.user
        }
      }

      return null
    } catch (error) {
      console.error('获取当前用户失败:', error)
      return null
    }
  }

  /**
   * 检查是否已认证
   */
  static isAuthenticated(): boolean {
    return this.getToken() !== null
  }

  /**
   * 检查是否为访客模式 - 修复过期检查逻辑
   */
  static isGuestMode(): boolean {
    const guestAuth = sessionStorage.getItem(this.STORAGE_KEYS.GUEST_AUTH)
    if (guestAuth) {
      try {
        const authData = JSON.parse(guestAuth) as AuthData
        return authData.token !== null && authData.user?.is_guest === true
      } catch {
        return false
      }
    }
    return false
  }
}
