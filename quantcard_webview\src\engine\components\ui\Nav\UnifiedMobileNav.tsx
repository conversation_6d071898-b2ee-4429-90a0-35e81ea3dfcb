/**
 * 🧭 统一移动端导航组�?
 * 集成顶部导航条、返回按钮、用户信息的一体化解决方案
 */

import React from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useUIState, useGameState } from '../../../../store/hooks'

interface UnifiedMobileNavProps {
  title: string
  showBackButton?: boolean
  backAction?: () => void
  showUserInfo?: boolean
  children?: React.ReactNode
}

// 🎨 主容�?
const Container = styled.div<{ theme: any }>`
  height: 100vh;
  background: ${p => p.theme.colors.background.void};
  color: ${p => p.theme.colors.text.primary};
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden; /* 防止整体页面滚动 */
`

// 📱 顶部导航�?
const TopNavBar = styled.div<{ theme: any }>`
  background: ${p => p.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-bottom: 1px solid ${p => p.theme.colors.border.primary};
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
  box-shadow: ${p => p.theme.shadows.medium};
  
  /* 状态栏适配 */
  padding-top: calc(1rem + env(safe-area-inset-top));
`

// ⬅️ 返回按钮
const BackButton = styled(motion.button)<{ theme: any }>`
  background: ${p => p.theme.colors.background.surface};
  border: 1px solid ${p => p.theme.colors.border.secondary};
  color: ${p => p.theme.colors.text.primary};
  border-radius: 12px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: ${p => p.theme.typography.fonts.primary};
  transition: all 0.2s ease;
  min-width: 80px;
  
  &:hover {
    background: ${p => p.theme.colors.primaryColors.cyan}20;
    border-color: ${p => p.theme.colors.primaryColors.cyan};
    transform: translateX(-2px);
  }
  
  .icon {
    font-size: 1.1rem;
  }
`

// 🏷�?页面标题
const PageTitle = styled.h1<{ theme: any }>`
  font-family: ${p => p.theme.typography.fonts.primary};
  font-size: 1.25rem;
  font-weight: 700;
  color: ${p => p.theme.colors.text.primary};
  margin: 0;
  text-align: center;
  flex: 1;
  
  /* 深色主题特殊效果 */
  ${p => p.theme.mode === 'dark' && `
    background: linear-gradient(135deg, ${p.theme.colors.primaryColors.cyan}, ${p.theme.colors.primaryColors.magenta});
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px ${p.theme.colors.primaryColors.cyan}40;
  `}
`

// 🔘 占位空间（保持布局平衡�?
const Spacer = styled.div`
  min-width: 80px;
`

// 💰 用户信息区域
const UserInfoArea = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 80px;
  justify-content: flex-end;
`

const CurrencyDisplay = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: ${p => p.theme.colors.background.surface};
  border: 1px solid ${p => p.theme.colors.border.secondary};
  border-radius: 16px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: ${p => p.theme.colors.text.primary};
  font-family: ${p => p.theme.typography.fonts.primary};
  
  .icon {
    font-size: 0.9rem;
  }
  
  .value {
    font-family: 'monospace';
  }
`

const UserAvatar = styled.div<{ theme: any }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: ${p => p.theme.colors.primaryColors.cyan}20;
  border: 2px solid ${p => p.theme.colors.primaryColors.cyan};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${p => p.theme.colors.primaryColors.cyan}30;
    transform: scale(1.05);
  }
`

// 📋 内容区域
const ContentArea = styled.div<{ theme: any }>`
  flex: 1;
  overflow-y: auto;
  position: relative;
  min-height: 0; /* 关键：允许flex子项收缩 */

  /* 美观的滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;

    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }
  }
`



// 🧭 统一导航组件
export default function UnifiedMobileNav({
  title,
  showBackButton = true,
  backAction,
  showUserInfo = true,
  children
}: UnifiedMobileNavProps) {
  const { switchScene } = useUIState()
  const { currency } = useGameState()

  const handleBack = () => {
    if (backAction) {
      backAction()
    } else {
      switchScene('WorldMap')
    }
  }

  const handleAvatarClick = () => {
    switchScene('Profile')
  }

  return (
    <Container>
      {/* 顶部导航�?*/}
      <TopNavBar>
        {showBackButton ? (
          <BackButton
            onClick={handleBack}
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.02 }}
          >
            <span className="icon">←</span>
            <span>返回</span>
          </BackButton>
        ) : (
          <Spacer />
        )}
        
        <PageTitle>{title}</PageTitle>
        
        {showUserInfo ? (
          <UserInfoArea>
            <CurrencyDisplay>
              <span className="icon">🪙</span>
              <span className="value">{currency?.coins || 0}</span>
            </CurrencyDisplay>
            <UserAvatar onClick={handleAvatarClick}>
              👤
            </UserAvatar>
          </UserInfoArea>
        ) : (
          <Spacer />
        )}
      </TopNavBar>

      {/* 内容区域 */}
      <ContentArea>
        {children}
      </ContentArea>
    </Container>
  )
}
