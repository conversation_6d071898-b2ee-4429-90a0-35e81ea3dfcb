# QuantCard 统一设计系统

## 🎯 设计原则

### 1. 移动优先 (Mobile-First)
- 所有场景都基于移动端设计，然后适配桌面端
- 使用响应式网格系统和弹性布局
- 触摸友好的交互元素 (最小44px点击区域)

### 2. 主题自适应 (Theme-Adaptive)
- 深色主题：回测场景 (赛博朋克风格)
- 浅色主题：其他所有场景 (现代简洁风格)
- 自动主题切换和流畅过渡动画

### 3. 一致性交互 (Consistent Interaction)
- 统一的导航模式
- 标准化的返回操作
- 一致的视觉反馈

## 🧭 统一导航系统

### UnifiedMobileNav 组件
```tsx
<UnifiedMobileNav
  title="页面标题"
  showBackButton={true}         // 是否显示返回按钮
  backAction={() => {}}         // 自定义返回操作
  showUserInfo={true}           // 是否显示用户信息
>
  {/* 页面内容 */}
</UnifiedMobileNav>
```

### 导航结构
```
┌─────────────────────────────────┐
│ [←返回]    页面标题    [用户信息] │
├─────────────────────────────────┤
│                                 │
│          页面内容区              │
│                                 │
└─────────────────────────────────┘
```

## 📱 场景规范

### 1. ProfileScene (个人资料)
- **导航**: 顶部导航栏
- **主题**: 浅色
- **布局**: 头像 + 统计 + 分页内容
- **特色**: 成就系统、活动记录、设置面板

### 2. BacktestScene (回测实验室)
- **导航**: 顶部导航栏
- **主题**: 深色 (赛博朋克)
- **布局**: 参数配置 + 3D图表
- **特色**: 回测参数、性能分析、导出功能

### 3. StrategyOptimizationScene (策略实验室)
- **导航**: 顶部导航栏
- **主题**: 浅色
- **布局**: 网格布局 (数据源 + 优化控制 + 实时分析)
- **特色**: 数据源配置、优化进度、KPI展示

### 4. StrategyCreationScene (策略工坊)
- **导航**: 不显示底部导航
- **主题**: 浅色
- **布局**: 工具栏 + 配置区 + 库存区
- **特色**: 拖拽操作、参数编辑、策略保存

### 5. CodexPanel (策略图鉴)
- **导航**: 底部导航高亮"图鉴"
- **主题**: 浅色
- **布局**: 搜索 + 筛选 + 卡牌网格
- **特色**: 收藏状态、详情弹窗、分类筛选

## 🎨 主题系统

### 深色主题 (CyberTheme)
```scss
// 主色系
primaryColors: {
  cyan: '#00FFFF',
  magenta: '#FF00FF', 
  purple: '#8A2BE2'
}

// 背景层次
background: {
  void: '#000008',
  card: '#16213E',
  surface: '#1A1A2E'
}

// 字体效果
typography: {
  fonts: {
    primary: 'Orbitron', // 科技感字体
    gaming: 'Orbitron'
  }
}
```

### 浅色主题 (LightTheme)
```scss
// 主色系
primaryColors: {
  cyan: '#00FFFF',
  magenta: '#FF00FF',
  purple: '#8A2BE2'
}

// 背景层次
background: {
  void: '#FFFFFF',
  card: 'rgba(255, 255, 255, 0.95)',
  surface: '#F8FAFC'
}

// 字体系统
typography: {
  fonts: {
    primary: 'Inter', // 现代字体
    gaming: 'Orbitron'
  }
}
```

## 🎭 动画规范

### 1. 页面过渡
- 场景切换: 赛博朋克扫描动画
- 持续时间: 0.8秒
- 缓动函数: `[0.25, 0.46, 0.45, 0.94]`

### 2. 组件动画
- 卡片悬停: `translateY(-4px)` + 阴影增强
- 按钮点击: `scale(0.95)`
- 列表项: 延迟动画 (index * 0.03s)

### 3. 状态反馈
- 加载状态: 渐变进度条
- 成功操作: 绿色边框闪烁
- 错误状态: 红色边框 + 抖动

## 📐 布局规范

### 响应式断点
```scss
breakpoints: {
  sm: '640px',   // 小屏手机
  md: '768px',   // 大屏手机/小平板
  lg: '1024px',  // 平板
  xl: '1280px'   // 桌面
}
```

### 网格系统
```scss
// 移动端: 单列
grid-template-columns: 1fr;

// 平板: 双列
@media (min-width: 768px) {
  grid-template-columns: 1fr 1fr;
}

// 桌面: 三列
@media (min-width: 1024px) {
  grid-template-columns: 1.2fr 1fr 1fr;
}
```

### 间距系统
```scss
spacing: {
  xs: '0.25rem',  // 4px
  sm: '0.5rem',   // 8px
  md: '1rem',     // 16px
  lg: '1.5rem',   // 24px
  xl: '2rem'      // 32px
}
```

## 🎯 交互规范

### 1. 导航交互
- **返回操作**:
  - 左上角返回按钮 → 世界地图
  - 支持自定义返回逻辑
  - 手势支持 (滑动返回)

- **用户信息**:
  - 右上角显示货币和头像
  - 点击头像进入个人资料
  - 实时更新用户状态

### 2. 反馈机制
- **触觉反馈**: 重要操作触发震动
- **视觉反馈**: 状态色彩 + 动画
- **音效反馈**: 操作确认音 (可选)

### 3. 手势支持
- **拖拽**: 策略卡牌拖拽操作
- **缩放**: 图表和地图缩放
- **滑动**: 页面切换和返回

## 🔧 开发规范

### 1. 组件命名
```tsx
// 统一前缀
<UnifiedMobileNav />
<CyberButton />
<UniversalCard />

// 场景后缀
<ProfileScene />
<BacktestScene />
<CodexPanel />
```

### 2. 样式规范
```tsx
// 主题类型声明
const Container = styled.div<{ theme: any }>`
  background: ${p => p.theme.colors.background.void};
  color: ${p => p.theme.colors.text.primary};
  font-family: ${p => p.theme.typography.fonts.primary};
`

// 响应式断点
@media (max-width: 640px) {
  grid-template-columns: 1fr;
}
```

### 3. 状态管理
```tsx
// 主题切换
const { switchTheme } = useTheme()
useEffect(() => {
  switchTheme('SceneName')
}, [switchTheme])

// 导航操作
const { switchScene } = useUIState()
switchScene('WorldMap')
```

## 📊 性能优化

### 1. 代码分割
- 场景级别懒加载
- 组件按需导入
- 资源预加载策略

### 2. 动画优化
- GPU 加速 (`transform`, `opacity`)
- 避免布局抖动
- 合理使用 `will-change`

### 3. 内存管理
- 组件卸载清理
- 事件监听器移除
- 图片资源优化

## 🎮 用户体验亮点

1. **流畅的主题切换**: 赛博朋克扫描动画
2. **一致的导航体验**: 统一的返回和导航逻辑
3. **直观的视觉层次**: 清晰的信息架构
4. **丰富的交互反馈**: 动画和状态提示
5. **无障碍设计**: 键盘导航和屏幕阅读器支持

---

*本设计系统确保 QuantCard 在所有场景中提供一致、直观、美观的用户体验。* 