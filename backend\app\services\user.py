from typing import Optional, Dict, Any, List
from datetime import datetime
from ..models.user import User
from ..utils.id_utils import MongoIDHandler
from ..utils.error_utils import <PERSON>rrorHandler
from ..utils.response_utils import ResponseFormatter
from ..core.auth import get_password_hash, verify_password

class UserService:
    """用户服务类"""

    @staticmethod
    async def create_user(data: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 检查用户名是否已存在
            existing_user = await User.find_one({"username": data["username"]})
            if existing_user:
                ErrorHandler.raise_operation_failed(
                    operation="创建",
                    entity="用户",
                    details={"error": "用户名已存在"}
                )

            # 处理密码
            data["hashed_password"] = get_password_hash(data.pop("password"))
            data["created_at"] = datetime.utcnow()
            data["updated_at"] = data["created_at"]

            # 创建用户
            user = User(**data)
            await user.save()

            # 格式化响应
            return ResponseFormatter.format_document(user.to_user_response().dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="创建",
                entity="用户",
                details={"error": str(e)}
            )

    @staticmethod
    async def get_user(user_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(user_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("用户", user_id)

            # 查找用户
            user = await User.get(obj_id)
            if not user:
                ErrorHandler.raise_not_found("用户", user_id)

            # 格式化响应
            return ResponseFormatter.format_document(user.to_user_response().dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="获取",
                entity="用户",
                details={"error": str(e)}
            )

    @staticmethod
    async def update_user(user_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户信息"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(user_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("用户", user_id)

            # 查找用户
            user = await User.get(obj_id)
            if not user:
                ErrorHandler.raise_not_found("用户", user_id)

            # 如果更新密码，需要处理
            if "password" in data:
                data["hashed_password"] = get_password_hash(data.pop("password"))

            # 更新时间
            data["updated_at"] = datetime.utcnow()

            # 更新用户
            await user.update({"$set": data})

            # 格式化响应
            return ResponseFormatter.format_document(user.to_user_response().dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="更新",
                entity="用户",
                details={"error": str(e)}
            )

    @staticmethod
    async def delete_user(user_id: str) -> None:
        """删除用户"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(user_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("用户", user_id)

            # 查找用户
            user = await User.get(obj_id)
            if not user:
                ErrorHandler.raise_not_found("用户", user_id)

            # 删除用户
            await user.delete()

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="删除",
                entity="用户",
                details={"error": str(e)}
            )

    # authenticate_user 方法已迁移到 app.core.auth 模块
    # 使用: from app.core.auth import authenticate_user

    @staticmethod
    async def list_users(
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取用户列表"""
        try:
            # 构建查询条件
            query = filters or {}

            # 使用聚合管道优化查询
            pipeline = [
                {"$match": query},
                {"$sort": {"created_at": -1}},
                {"$skip": skip},
                {"$limit": limit}
            ]

            # 执行查询
            users = await User.aggregate(pipeline).to_list(length=None)
            total = await User.count_documents(query)

            # 格式化响应
            formatted_users = [
                ResponseFormatter.format_document(user.to_user_response().dict())
                for user in users
            ]

            return formatted_users, total

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="获取",
                entity="用户列表",
                details={"error": str(e)}
            ) 