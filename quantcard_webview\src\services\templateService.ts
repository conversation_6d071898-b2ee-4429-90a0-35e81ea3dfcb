/**
 * 统一的策略模板数据服务
 * 负责管理所有策略模板的获取、缓存和转换
 */

import { buildCoreSentence } from '../utils/templateSentence'
import { AuthTokenManager } from '../utils/authUtils'

export interface TemplateData {
  template_id: string
  name: string
  description: string
  version?: string
  author?: string
  stars?: number
  tags?: string[]
  parameters?: Record<string, any>
  parameterGroups?: Record<string, any>
  ui?: any
  is_active?: boolean
}

export interface UnifiedTemplateData extends TemplateData {
  id: string // 统一的id字段，等于template_id
  category: string // 从tags推导出的分类
  rarity: string // 从stars推导出的稀有度
  icon: string // 从category推导出的图标
  coreSentence: string // 生成的核心句式
}

class TemplateService {
  private cache: Map<string, UnifiedTemplateData> = new Map()
  private loading: Set<string> = new Set()
  private allTemplatesCache: UnifiedTemplateData[] | null = null
  private lastFetchTime = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 获取单个模板数据
   */
  async getTemplate(templateId: string): Promise<UnifiedTemplateData | null> {
    // 检查缓存
    if (this.cache.has(templateId)) {
      return this.cache.get(templateId)!
    }

    // 避免重复请求
    if (this.loading.has(templateId)) {
      // 等待正在进行的请求
      while (this.loading.has(templateId)) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return this.cache.get(templateId) || null
    }

    this.loading.add(templateId)

    try {
      // 使用统一的认证工具获取认证头
      const authHeaders = AuthTokenManager.getAuthHeaders()
      console.log(`🔍 获取模板 ${templateId}，认证头:`, authHeaders)

      const response = await fetch(`/api/v1/strategies/templates/${templateId}`, {
        headers: authHeaders
      })

      if (response.status === 401) {
        console.log('🔄 Token过期，清理认证数据')
        AuthTokenManager.clearAllAuthData()
        window.dispatchEvent(new CustomEvent('auth-expired'))
        throw new Error('TOKEN_EXPIRED')
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      if (!result.success || !result.data) {
        throw new Error('Invalid response format')
      }

      const unified = this.unifyTemplateData(result.data)
      this.cache.set(templateId, unified)
      return unified

    } catch (error) {
      console.warn(`获取模板 ${templateId} 失败:`, error)
      return null
    } finally {
      this.loading.delete(templateId)
    }
  }

  /**
   * 获取所有模板数据
   */
  async getAllTemplates(forceRefresh = false): Promise<UnifiedTemplateData[]> {
    const now = Date.now()

    // 检查缓存
    if (!forceRefresh && this.allTemplatesCache && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.allTemplatesCache
    }

    try {
      // 使用统一的认证工具获取认证头
      const authHeaders = AuthTokenManager.getAuthHeaders()
      console.log('🔍 获取所有模板，认证头:', authHeaders)

      const response = await fetch('/api/v1/strategies/templates?limit=100', {
        headers: authHeaders
      })

      if (response.status === 401) {
        console.log('🔄 Token过期，清理认证数据')
        AuthTokenManager.clearAllAuthData()
        window.dispatchEvent(new CustomEvent('auth-expired'))
        throw new Error('TOKEN_EXPIRED')
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      if (!result.success || !result.items) {
        throw new Error('Invalid response format')
      }

      const templates = result.items.map((template: TemplateData) => this.unifyTemplateData(template))

      // 更新缓存
      this.allTemplatesCache = templates
      this.lastFetchTime = now

      // 同时更新单个模板缓存
      templates.forEach(template => {
        this.cache.set(template.template_id, template)
      })

      return templates

    } catch (error) {
      console.error('获取所有模板失败:', error)
      return this.allTemplatesCache || []
    }
  }

  /**
   * 批量获取模板数据
   */
  async getTemplates(templateIds: string[]): Promise<Record<string, UnifiedTemplateData>> {
    const result: Record<string, UnifiedTemplateData> = {}
    const toFetch: string[] = []

    // 检查缓存
    for (const id of templateIds) {
      if (this.cache.has(id)) {
        result[id] = this.cache.get(id)!
      } else {
        toFetch.push(id)
      }
    }

    // 批量获取未缓存的数据
    if (toFetch.length > 0) {
      const promises = toFetch.map(id => this.getTemplate(id))
      const templates = await Promise.all(promises)
      
      templates.forEach((template, index) => {
        if (template) {
          result[toFetch[index]] = template
        }
      })
    }

    return result
  }

  /**
   * 统一模板数据格式
   */
  private unifyTemplateData(template: TemplateData): UnifiedTemplateData {
    // 从tags推导分类
    const category = this.getCategoryFromTags(template.tags || [])

    // 从stars推导稀有度
    const rarity = this.getRarityFromStars(template.stars || 1)

    // 从分类推导图标
    const icon = this.getIconFromCategory(category)

    // 生成核心句式
    const coreSentence = this.generateCoreSentence(template)

    return {
      ...template,
      id: template.template_id, // 统一id字段
      category,
      rarity,
      icon,
      coreSentence
    }
  }

  /**
   * 从tags推导分类
   */
  private getCategoryFromTags(tags: string[]): string {
    if (tags.includes('选股') || tags.includes('filter')) return 'filter'
    if (tags.includes('择时') || tags.includes('timing')) return 'timing'
    if (tags.includes('风控') || tags.includes('risk')) return 'risk_management'
    return 'timing' // 默认分类
  }

  /**
   * 从stars推导稀有度
   */
  private getRarityFromStars(stars: number): string {
    if (stars >= 5) return 'legendary'
    if (stars >= 4) return 'epic'
    if (stars >= 3) return 'rare'
    return 'common'
  }

  /**
   * 从分类推导图标
   */
  private getIconFromCategory(category: string): string {
    const iconMap: Record<string, string> = {
      filter: '🔍',
      timing: '⏰',
      risk_management: '🛡️'
    }
    return iconMap[category] || '🃏'
  }



  /**
   * 生成核心句式
   */
  private generateCoreSentence(template: TemplateData): string {
    try {
      if (template.parameters && template.parameterGroups) {
        return buildCoreSentence(template.parameters, template.parameterGroups, {})
      }
    } catch (error) {
      console.warn(`生成模板 ${template.template_id} 的核心句式失败:`, error)
    }
    return template.description || ''
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.allTemplatesCache = null
    this.lastFetchTime = 0
  }

  /**
   * 预加载常用模板
   */
  async preloadCommonTemplates(token?: string): Promise<void> {
    try {
      await this.getAllTemplates(token)
    } catch (error) {
      console.warn('预加载模板失败:', error)
    }
  }
}

// 导出单例实例
export const templateService = new TemplateService()

// 导出便捷方法
export const getTemplate = (templateId: string) =>
  templateService.getTemplate(templateId)

export const getAllTemplates = (forceRefresh = false) =>
  templateService.getAllTemplates(forceRefresh)

export const getTemplates = (templateIds: string[]) =>
  templateService.getTemplates(templateIds)
