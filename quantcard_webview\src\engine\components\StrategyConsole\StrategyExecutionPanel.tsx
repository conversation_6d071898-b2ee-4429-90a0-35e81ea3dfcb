import React, { useEffect, useState, useCallback, useRef } from 'react';
import { unifiedWebSocket } from '../../../services/unifiedWebSocket';
import styled from 'styled-components';

interface Log {
  id: string;
  message: string;
  timestamp: string;
}

interface Signal {
  id: string;
  symbol: string;
  direction: string;
  type: string;
  description?: string;
  timestamp: string;
  [key: string]: any;
}

interface StrategyExecutionPanelProps {
  strategyId: string;
  onViewResults?: () => void;
  onClose?: () => void;
}

// 将日志数据缓存最大条数
const MAX_LOGS = 100;
// 显示的最大日志条数
const DISPLAY_LOGS = 10;

// 样式组件 - 直接复制旧版本的样式
const PanelContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

// 新的头部布局 - 执行日志标题 + 已连接标签 + 关闭按钮
const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const LogsTitle = styled.h4`
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StatusTag = styled.div<{ $color: string }>`
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  background: ${props => props.$color === 'success' ? '#f6ffed' : props.$color === 'error' ? '#fff2f0' : '#f0f0f0'};
  color: ${props => props.$color === 'success' ? '#52c41a' : props.$color === 'error' ? '#ff4d4f' : '#666'};
  border: 1px solid ${props => props.$color === 'success' ? '#b7eb8f' : props.$color === 'error' ? '#ffccc7' : '#d9d9d9'};
`;

const RetryButton = styled.button`
  padding: 2px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  &:hover {
    background: #e6f7ff;
    color: #1890ff;
  }
`;

const LoadingContainer = styled.div`
  text-align: center;
  padding: 20px;
  color: #666;
`;

const ErrorAlert = styled.div`
  margin: 10px 0;
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const LogsContainer = styled.div<{ $logCount: number }>`
  height: ${props => {
    // 智能高度计算：基础高度 + 日志行数 * 行高，最小120px，最大320px
    const baseHeight = 60; // 基础高度
    const lineHeight = 24; // 每行高度（减少行距）
    const calculatedHeight = baseHeight + (props.$logCount * lineHeight);
    return Math.min(Math.max(calculatedHeight, 120), 320) + 'px';
  }};
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
`;

const NoData = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-size: 14px;
`;

const LogItem = styled.div`
  border-bottom: 1px dashed #eee;
  padding: 2px 0; /* 减少行距 */
  line-height: 1.3; /* 减少行高 */

  &:last-child {
    border-bottom: none;
  }
`;

const LogTime = styled.span`
  margin-right: 8px;
  color: #1890ff;
  font-size: 12px;
  min-width: 65px;
  display: inline-block;
`;

const LogMessage = styled.span`
  font-size: 13px;
  color: #333;
`;

const StrategyExecutionPanel: React.FC<StrategyExecutionPanelProps> = ({
  strategyId,
  onViewResults,
  onClose
}) => {
  const [logs, setLogs] = useState<Log[]>([]);
  const [signals, setSignals] = useState<Signal[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'completed' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [signalCount, setSignalCount] = useState(0);
  
  // 添加日志容器引用，用于滚动控制
  const logsContainerRef = useRef<HTMLDivElement>(null);
  
  // 添加日志的回调函数（使用useCallback避免重复创建）
  const addLog = useCallback((log: Log) => {
    setLogs(prev => {
      const newLogs = [...prev, log];
      // 限制日志数量
      return newLogs.length > MAX_LOGS ? newLogs.slice(-MAX_LOGS) : newLogs;
    });
    
    // 滚动到最新的日志
    setTimeout(() => {
      if (logsContainerRef.current) {
        logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
      }
    }, 10);
    
    // 如果状态是idle并且收到日志，说明策略正在执行
    setExecutionStatus(status => status === 'idle' ? 'running' : status);
    
    // 检查日志消息是否包含信号数量的信息
    const logMessage = log.message?.toLowerCase() || '';
    
    // 更精确的正则表达式，匹配常见的信号数量格式
    const signalMatchers = [
      /生成\s*(\d+)\s*个\s*信号/,      // 匹配"生成 X 个信号"
      /筛选出\s*(\d+)\s*[只个]股票/,   // 匹配"筛选出 X 只股票"或"筛选出 X 个股票"
      /生成\s*(\d+)\s*[只个]/,         // 匹配"生成 X 只"或"生成 X 个"
      /策略.+耗时:.+生成\s*(\d+)\s*个/ // 匹配"策略组执行完成，耗时: x.xx秒，生成 X 个信号"
    ];
    
    for (const matcher of signalMatchers) {
      const match = logMessage.match(matcher);
      if (match && match[1]) {
        const count = parseInt(match[1], 10);
        if (!isNaN(count)) {
          setSignalCount(count);
          break;
        }
      }
    }
  }, []);

  // 添加信号的回调函数
  const addSignal = useCallback((signal: Signal) => {
    setSignals(prev => [...prev, signal]);
  }, []);

  // 处理WebSocket消息
  const handleMessage = useCallback((message: any) => {
    console.log('收到WebSocket消息:', message);
    
    try {
      if (message.type === 'strategy_update' && message.group_id === strategyId) {
        if (message.update_type === 'log') {
          // 更新日志
          addLog({
            id: `log-${Date.now()}-${Math.random()}`,
            message: message.log,
            timestamp: message.timestamp || new Date().toISOString()
          });
        } else if (message.update_type === 'signal') {
          // 更新信号
          addSignal({
            ...message.signal,
            id: message.signal.id || `signal-${Date.now()}-${Math.random()}`,
            timestamp: message.timestamp || new Date().toISOString()
          });
          
          // 如果有signal_count字段，更新信号计数
          if (message.signal_count || message.total_signals) {
            setSignalCount(message.signal_count || message.total_signals || 0);
          }
        } else if (message.update_type === 'result') {
          // 更新执行状态
          setExecutionStatus(message.success ? 'completed' : 'error');
          
          // 如果有信号计数字段，更新信号计数
          if (message.signal_count || message.total_signals) {
            setSignalCount(message.signal_count || message.total_signals || 0);
          }
        }
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
    }
  }, [strategyId, addLog, addSignal]);

  // 重试连接
  const handleRetry = useCallback(() => {
    setError(null);
    setLoading(true);
    
    // 重新连接WebSocket
    if (!unifiedWebSocket.connected) {
      unifiedWebSocket.connect();
    }
    
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  }, []);

  // WebSocket连接管理
  useEffect(() => {
    const ws = unifiedWebSocket;

    // 连接状态监听
    const unsubscribeConnection = ws.onConnection((connected: boolean) => {
      setIsConnected(connected);
      if (connected) {
        setError(null);
        setLoading(false);
        console.log('✅ WebSocket连接已建立，策略ID:', strategyId);
      } else {
        console.log('❌ WebSocket连接断开，策略ID:', strategyId);
      }
    });

    // 错误监听
    const unsubscribeError = ws.onError((error: Error) => {
      console.error('❌ WebSocket错误:', error);
      setError(error.message);
      setIsConnected(false);
      setLoading(false);
    });

    // 消息监听
    const unsubscribeMessage = ws.subscribe('strategy_update', handleMessage);

    // 连接WebSocket - 确保连接稳定
    if (!ws.connected && !ws.connecting) {
      console.log('🔄 开始连接WebSocket，策略ID:', strategyId);
      setLoading(true);
      ws.connect().catch((error) => {
        console.error('WebSocket连接失败:', error);
        setError('连接失败，请重试');
        setLoading(false);
      });
    } else if (ws.connected) {
      setIsConnected(true);
      setLoading(false);
    }

    return () => {
      // 清理监听器
      unsubscribeConnection();
      unsubscribeError();
      unsubscribeMessage();
    };
  }, [handleMessage, strategyId]);
  
  // 计算显示的日志数量，用于智能高度
  const displayLogs = logs.slice(-DISPLAY_LOGS);
  const logCount = Math.max(displayLogs.length, 3); // 最少显示3行的高度

  // 渲染函数
  return (
    <PanelContainer className="strategy-execution-panel">
      {/* 新的头部布局：执行日志标题 + 已连接标签 + 关闭按钮 */}
      <PanelHeader>
        <HeaderLeft>
          <LogsTitle>执行日志</LogsTitle>
          <StatusTag $color={isConnected ? "success" : "error"}>
            <span>{isConnected ? '✓' : '✗'}</span>
            {isConnected ? '已连接' : '未连接'}
          </StatusTag>
          {!isConnected && (
            <RetryButton onClick={handleRetry} title="重试连接">
              <span>🔄</span>
              重试
            </RetryButton>
          )}
        </HeaderLeft>

        <HeaderRight>
          {onClose && (
            <CloseButton onClick={onClose} title="关闭执行面板">
              ✕
            </CloseButton>
          )}
        </HeaderRight>
      </PanelHeader>

      {loading && (
        <LoadingContainer>
          <div>⏳ 正在连接...</div>
        </LoadingContainer>
      )}

      {error && !loading && (
        <ErrorAlert>
          <div>
            <strong>连接错误</strong>
            <div>{error}</div>
          </div>
          <RetryButton onClick={handleRetry}>
            重试
          </RetryButton>
        </ErrorAlert>
      )}

      {/* 智能高度的日志容器 */}
      <LogsContainer ref={logsContainerRef} $logCount={logCount}>
        {logs.length === 0 ? (
          <NoData>
            {loading ? '连接中...' : (
              executionStatus === 'running' || logs.length > 0 ? '加载日志中...' : '暂无日志'
            )}
          </NoData>
        ) : (
          // 只显示最近的DISPLAY_LOGS条日志
          displayLogs.map(log => (
            <LogItem key={log.id}>
              <LogTime>
                {new Date(log.timestamp).toLocaleTimeString()}
              </LogTime>
              <LogMessage>{log.message}</LogMessage>
            </LogItem>
          ))
        )}
      </LogsContainer>
    </PanelContainer>
  );
};

export default StrategyExecutionPanel;
