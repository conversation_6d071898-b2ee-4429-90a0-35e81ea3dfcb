# 策略控制台模态框优化测试指南

## 已完成的改进

### 1. 执行面板布局优化 ✅
- **原布局**: 策略执行标题 + 关闭按钮 → 已连接标签 → 横线 → 执行日志标题 + 日志信息
- **新布局**: 执行日志标题 + 已连接标签 + 关闭按钮 → 日志信息
- **改进**: 减少了一行标题，信息密度提升

### 2. 执行日志行距优化 ✅
- **原行距**: `padding: 4px 0`
- **新行距**: `padding: 2px 0` + `line-height: 1.3`
- **改进**: 行距减少50%，显示更多日志内容

### 3. 智能日志面板高度 ✅
- **原高度**: 固定320px
- **新高度**: 基于日志行数动态计算，最小120px，最大320px
- **公式**: `基础高度60px + 日志行数 × 24px`
- **改进**: 日志少时面板更小，日志多时自动扩展

### 4. 模态框头部优化 ✅
- **StrategySettingsModal**:
  - 头部padding: `1.5rem` → `1rem 1.25rem`
  - 标题字体: `1.25rem/700` → `1.1rem/600`
  - 关闭按钮: `40px` → `32px`
  - 添加背景色: `#f8fafc`

- **StrategyViewModal**:
  - 同样的头部优化
  - 最大高度: `60vh` → `75vh`

### 5. 表单组件密度优化 ✅
- **FormGroup间距**: `1.5rem` → `1rem`
- **Label字体**: `0.875rem` → `0.8rem`
- **Input padding**: `0.75rem 1rem` → `0.625rem 0.875rem`
- **TextArea最小高度**: `80px` → `60px`
- **按钮padding**: `0.75rem 1.5rem` → `0.625rem 1.25rem`

### 6. 策略参数编辑功能 ✅
- **新增标签页**: 基本设置 + 参数配置
- **参数编辑器**: 集成InlineParameterEditor组件
- **模板加载**: 自动加载策略卡片的模板配置
- **参数保存**: 更新时同时保存卡片参数

### 7. 历史信号查看功能 ✅
- **点击交互**: 执行记录可点击查看详细信号
- **信号展示**: 根据策略类型显示选股或择时信号表
- **返回导航**: 支持从信号详情返回历史列表
- **视觉提示**: 有信号的记录显示"点击查看"提示

## 测试要点

### 功能测试
1. **执行面板**:
   - [ ] 点击策略执行按钮，面板正确显示
   - [ ] 头部布局：执行日志 + 已连接 + 关闭按钮在同一行
   - [ ] 日志高度根据内容智能调整
   - [ ] 关闭按钮正常工作

2. **设置模态框**:
   - [ ] 基本设置标签页正常显示和编辑
   - [ ] 参数配置标签页正确加载策略卡片参数
   - [ ] 参数编辑器正常工作
   - [ ] 保存时同时更新基本信息和参数

3. **查看模态框**:
   - [ ] 执行历史正常显示
   - [ ] 点击有信号的执行记录能查看详细信号
   - [ ] 信号表格正确显示（选股/择时）
   - [ ] 返回按钮正常工作

### 样式测试
1. **信息密度**:
   - [ ] 模态框头部更紧凑
   - [ ] 表单元素间距合理
   - [ ] 历史记录项更紧凑

2. **响应式**:
   - [ ] 移动端显示正常
   - [ ] 触摸交互流畅

## 潜在问题排查

1. **模板加载失败**: 检查fetchStrategyTemplate API
2. **参数编辑器错误**: 检查InlineParameterEditor组件导入
3. **信号表格显示问题**: 检查SignalTable/TimingSignalTable组件
4. **WebSocket连接**: 确保执行面板WebSocket正常工作
