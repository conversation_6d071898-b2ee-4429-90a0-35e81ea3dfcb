"""
策略调度器服务

用于定时执行策略，支持按交易时段智能调度
"""
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
import uuid
import traceback

from .trading_calendar import trading_calendar
from .in_memory_queue import in_memory_queue

logger = logging.getLogger(__name__)

class StrategyScheduler:
    """策略调度器，用于管理策略的定时执行"""
    
    _instance = None
    
    # 支持的定时周期（秒）
    SUPPORTED_PERIODS = {
        "1min": 60,
        "5min": 300,
        "15min": 900,
        "30min": 1800,
        "60min": 3600,
        "1h": 3600,
        "2h": 7200,
        "4h": 14400,
        "1d": 86400
    }
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """初始化调度器"""
        self._tasks: Dict[str, Dict[str, Any]] = {}
        self._running = False
        self._lock = asyncio.Lock()
        self._monitor_task = None
        
    async def start(self):
        """启动调度器"""
        if self._running:
            return

        self._running = True
        logger.info("策略调度器已启动")
        
    async def stop(self):
        """停止调度器"""
        if not self._running:
            return
            
        self._running = False
        
        # 取消所有任务
        for task_id, task_info in list(self._tasks.items()):
            if task_info.get("task") and not task_info["task"].done():
                task_info["task"].cancel()
                
        self._tasks.clear()
        logger.info("策略调度器已停止")
    
    async def _publish_log(self, user_id: str, strategy_id: str, message: str):
        """发布策略相关日志到WebSocket

        Args:
            user_id: 用户ID
            strategy_id: 策略ID (对于调度器，这就是group_id)
            message: 日志消息
        """
        await in_memory_queue.publish("strategy_updates", {
            "type": "strategy_update",
            "update_type": "log",
            "user_id": user_id,
            "strategy_id": strategy_id,
            "group_id": strategy_id,  # 修复：使用strategy_id作为group_id，确保前端能接收到
            "log": message,
            "timestamp": datetime.now().isoformat(),
            "is_public": False
        })
    
    async def schedule_strategy(
        self,
        strategy_id: str,
        period: str,
        callback: Callable,
        parameters: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> str:
        """
        调度策略执行
        
        Args:
            strategy_id: 策略ID
            period: 执行周期，如 1min, 5min, 15min, 30min, 60min, 1h, 4h, 1d
            callback: 回调函数，接受策略ID和参数
            parameters: 策略参数
            metadata: 元数据
            user_id: 用户ID
            task_id: 任务ID，不指定则自动生成
            
        Returns:
            str: 任务ID
        """
        if not self._running:
            await self.start()
            
        # 验证周期
        if period not in self.SUPPORTED_PERIODS:
            valid_periods = ", ".join(self.SUPPORTED_PERIODS.keys())
            raise ValueError(f"不支持的周期: {period}，有效值: {valid_periods}")
            
        # 生成任务ID
        if not task_id:
            task_id = str(uuid.uuid4())
            
        async with self._lock:
            # 创建任务配置
            next_run = self._calculate_next_run(period, strategy_id, user_id)
            self._tasks[task_id] = {
                "id": task_id,
                "strategy_id": strategy_id,
                "user_id": user_id,
                "period": period,
                "period_seconds": self.SUPPORTED_PERIODS[period],
                "callback": callback,
                "parameters": parameters or {},
                "metadata": metadata or {},
                "next_run": next_run, 
                "last_run": None,
                "active": True,
                "error_count": 0,
                "created_at": datetime.now(),
                "task": asyncio.create_task(self._execute_later(task_id, (next_run - datetime.now()).total_seconds()))
            }
            
            logger.info(f"调度策略: ID={strategy_id}, 周期={period}, 下次执行时间={next_run}")
            
            # 发送日志
            log_message = f"调度策略: ID={strategy_id}, 周期={period}, 下次执行时间={next_run}"
            await self._publish_log(user_id or "system", strategy_id, log_message)
            
            return task_id
            
    async def unschedule_strategy(self, task_id: str) -> bool:
        """
        取消策略调度
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"找不到指定的任务: {task_id}")
                return False
                
            # 取消任务
            task_info = self._tasks[task_id]
            if task_info.get("task") and not task_info["task"].done():
                task_info["task"].cancel()
                
            # 移除任务
            del self._tasks[task_id]
            logger.info(f"已取消策略调度: {task_id}")
            
            return True
            
    async def pause_strategy(self, task_id: str) -> bool:
        """
        暂停策略执行
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"找不到指定的任务: {task_id}")
                return False
                
            # 暂停任务
            task_info = self._tasks[task_id]
            if task_info.get("task") and not task_info["task"].done():
                task_info["task"].cancel()
            
            task_info["active"] = False
            logger.info(f"已暂停策略执行: {task_id}")
            
            return True
            
    async def resume_strategy(self, task_id: str) -> bool:
        """
        恢复策略执行
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"找不到指定的任务: {task_id}")
                return False
                
            # 恢复任务
            task_info = self._tasks[task_id]
            task_info["active"] = True
            
            # 重新计算下次执行时间
            user_id = task_info.get("user_id", "system")
            strategy_id = task_info.get("strategy_id", "")
            next_run = self._calculate_next_run(task_info["period"], strategy_id, user_id)
            task_info["next_run"] = next_run
            
            # 创建新任务
            wait_seconds = max(0, (next_run - datetime.now()).total_seconds())
            task_info["task"] = asyncio.create_task(self._execute_later(task_id, wait_seconds))
            
            logger.info(f"已恢复策略执行: {task_id}, 下次执行时间: {next_run}")
            
            return True
            
    async def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务信息，找不到则返回None
        """
        if task_id not in self._tasks:
            return None
            
        task_info = self._tasks[task_id].copy()
        # 移除不可序列化的字段
        task_info.pop("task", None)
        task_info.pop("callback", None)
        
        return task_info
        
    async def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务
        
        Returns:
            List[Dict]: 任务列表
        """
        result = []
        for task_id, task_info in self._tasks.items():
            info = task_info.copy()
            # 移除不可序列化的字段
            info.pop("task", None)
            info.pop("callback", None)
            result.append(info)
            
        return result
        
    def _calculate_next_run(self, period: str, strategy_id: Optional[str] = None, user_id: Optional[str] = None) -> datetime:
        """
        计算下次执行时间
        
        Args:
            period: 执行周期
            strategy_id: 策略ID，用于日志推送
            user_id: 用户ID，用于日志推送
            
        Returns:
            datetime: 下次执行时间
        """
        now = datetime.now()
        
        # 检查市场状态
        market_status = trading_calendar.get_market_status()
        is_trading_hours = market_status["is_trading_hours"]
        
        # 如果不是交易时段，则返回下一个交易时段的时间
        if not is_trading_hours:
            next_trading_time = trading_calendar.get_next_trading_time()
            log_message = f"非交易时段，下次执行时间设为下一个交易时段: {next_trading_time}"
            logger.info(log_message)
            
            # 如果有策略ID和用户ID，直接推送日志
            if strategy_id and user_id:
                asyncio.create_task(self._publish_log(user_id, strategy_id, log_message))
            
            return next_trading_time
            
        # 交易时段内，计算下一个时间点
        period_seconds = self.SUPPORTED_PERIODS[period]
        
        if period == "1d":
            # 日线级别特殊处理，设置为当天收盘时间
            today = now.date()
            closing_time = datetime.combine(today, trading_calendar.AFTERNOON_END)
            if now < closing_time:
                return closing_time
            else:
                # 已过收盘时间，设置为下一个交易日的收盘时间
                next_day = trading_calendar.get_next_trading_day()
                return datetime.combine(next_day, trading_calendar.AFTERNOON_END)
                
        # 分钟级别周期，对齐到周期整点
        seconds_past_hour = (now.minute * 60 + now.second) % period_seconds
        next_run = now + timedelta(seconds=(period_seconds - seconds_past_hour))
        
        # 检查是否超出交易时间
        if not trading_calendar.is_trading_hours(next_run):
            logger.info(f"计算的下次执行时间 {next_run} 超出交易时段，调整为下一个交易时段")
            next_run = trading_calendar.get_next_trading_time()
            
        return next_run
            
    async def _execute_later(self, task_id: str, wait_seconds: float) -> None:
        """
        延迟执行任务
        
        Args:
            task_id: 任务ID
            wait_seconds: 等待秒数
        """
        try:
            # 等待指定时间
            await asyncio.sleep(wait_seconds)
            
            # 获取任务信息
            async with self._lock:
                if task_id not in self._tasks:
                    return
                    
                task_info = self._tasks[task_id]
                if not task_info["active"]:
                    return
            
            # 获取用户ID和策略ID - 提前获取，以便在多处使用
            user_id = task_info.get("user_id", "system")
            strategy_id = task_info.get("strategy_id", "")
                    
            # 检查是否仍在交易时段
            if not trading_calendar.is_trading_hours() and task_info["period"] != "1d":
                logger.info(f"任务 {task_id} 到达执行时间，但已不在交易时段，将重新调度")
                
                # 推送日志，包括非交易时段信息
                log_message = f"任务 {task_id} 到达执行时间，但已不在交易时段，将重新调度"
                await self._publish_log(user_id, strategy_id, log_message)
                
                # 直接推送非交易时段日志
                next_run = self._calculate_next_run(task_info["period"], strategy_id, user_id)
                log_message = f"非交易时段，下次执行时间设为下一个交易时段: {next_run}"
                await self._publish_log(user_id, strategy_id, log_message)
                
                async with self._lock:
                    if task_id in self._tasks:
                        task_info = self._tasks[task_id]
                        task_info["next_run"] = next_run
                        wait_seconds = max(0, (next_run - datetime.now()).total_seconds())
                        task_info["task"] = asyncio.create_task(
                            self._execute_later(task_id, wait_seconds)
                        )
                return
                
            # 更新最后执行时间
            task_info["last_run"] = datetime.now()
            
            # 执行任务
            strategy_id = task_info["strategy_id"]
            parameters = task_info["parameters"]
            metadata = task_info["metadata"]
            
            logger.info(f"开始执行任务: {task_id}, 策略: {strategy_id}")
            
            # 推送日志
            log_message = f"开始执行任务: {task_id}, 策略: {strategy_id}"
            await self._publish_log(user_id, strategy_id, log_message)
            
            # 调用回调函数
            try:
                await task_info["callback"](
                    strategy_id=strategy_id,
                    parameters=parameters,
                    metadata=metadata,
                    user_id=user_id
                )
                # 重置错误计数
                task_info["error_count"] = 0
            except Exception as e:
                # 增加错误计数
                task_info["error_count"] += 1
                logger.error(f"执行任务 {task_id} 失败: {str(e)}")
                logger.error(traceback.format_exc())
                
                # 推送错误日志
                error_message = f"执行任务 {task_id} 失败: {str(e)}"
                await self._publish_log(user_id, strategy_id, error_message)
                
                # 如果连续错误超过一定次数，暂停任务
                if task_info["error_count"] >= 5:
                    logger.warning(f"任务 {task_id} 连续失败超过5次，暂停执行")
                    async with self._lock:
                        if task_id in self._tasks:
                            self._tasks[task_id]["active"] = False
                    return
            
            # 计算下次执行时间并调度
            next_run = self._calculate_next_run(task_info["period"], strategy_id, user_id)
            logger.info(f"任务 {task_id} 执行完成，下次执行时间: {next_run}")
            
            # 推送日志
            completion_message = f"任务 {task_id} 执行完成，下次执行时间: {next_run}"
            await self._publish_log(user_id, strategy_id, completion_message)
            
            async with self._lock:
                if task_id in self._tasks and self._tasks[task_id]["active"]:
                    task_info = self._tasks[task_id]
                    task_info["next_run"] = next_run
                    wait_seconds = max(0, (next_run - datetime.now()).total_seconds())
                    task_info["task"] = asyncio.create_task(
                        self._execute_later(task_id, wait_seconds)
                    )
                
        except asyncio.CancelledError:
            # 任务被取消
            logger.info(f"任务 {task_id} 被取消")
        except Exception as e:
            # 捕获其他异常
            logger.error(f"执行任务 {task_id} 时发生异常: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 尝试重新调度
            try:
                async with self._lock:
                    if task_id in self._tasks:
                        task_info = self._tasks[task_id]
                        task_info["error_count"] += 1
                        
                        if task_info["error_count"] < 5:
                            next_run = datetime.now() + timedelta(minutes=1)
                            task_info["next_run"] = next_run
                            wait_seconds = 60  # 一分钟后重试
                            task_info["task"] = asyncio.create_task(
                                self._execute_later(task_id, wait_seconds)
                            )
                        else:
                            # 暂停执行
                            task_info["active"] = False
                            logger.warning(f"任务 {task_id} 连续失败超过5次，暂停执行")
            except Exception as e2:
                logger.error(f"重新调度任务 {task_id} 失败: {str(e2)}")

# 创建单例实例
strategy_scheduler = StrategyScheduler.get_instance()

# 异步工厂函数，用于依赖注入
async def get_strategy_scheduler() -> StrategyScheduler:
    """获取策略调度器实例"""
    if not strategy_scheduler._running:
        await strategy_scheduler.start()
    return strategy_scheduler 